# Workspace Management System .htaccess
# إعدادات Apache للموقع

# تفعيل إعادة الكتابة
RewriteEngine On

# إعدادات الأمان العامة
# General Security Settings

# منع الوصول للملفات الحساسة
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|backup|old|sql)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# حماية ملفات PHP الحساسة
<Files "config.php">
    Order Deny,Allow
    Deny from all
</Files>

<Files "settings.php">
    Order Deny,Allow
    Deny from all
</Files>

# منع عرض محتويات المجلدات
Options -Indexes

# إعدادات الأمان للرؤوس
# Security Headers

<IfModule mod_headers.c>
    # منع XSS
    Header always set X-XSS-Protection "1; mode=block"
    
    # منع MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # إخفاء معلومات الخادم
    Header always unset X-Powered-By
    Header always unset Server
    
    # حماية من Clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # HTTPS Strict Transport Security (إذا كان HTTPS مفعل)
    # Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains"
</IfModule>

# ضغط الملفات لتحسين الأداء
# File Compression for Performance

<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# إعدادات التخزين المؤقت
# Cache Settings

<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # HTML
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# إعدادات PHP
# PHP Settings

<IfModule mod_php7.c>
    # زيادة حد الذاكرة
    php_value memory_limit 256M
    
    # زيادة وقت التنفيذ
    php_value max_execution_time 300
    
    # إعدادات رفع الملفات
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # إخفاء أخطاء PHP في الإنتاج
    php_flag display_errors Off
    php_flag log_errors On
    
    # تفعيل الجلسات
    php_flag session.auto_start Off
</IfModule>

# إعادة توجيه HTTPS (إذا كان متوفراً)
# HTTPS Redirect (if available)

# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# إعادة توجيه www (اختياري)
# WWW Redirect (optional)

# RewriteCond %{HTTP_HOST} ^www\.(.*)$ [NC]
# RewriteRule ^(.*)$ https://%1/$1 [R=301,L]

# قواعد إعادة الكتابة للروابط الودية
# URL Rewriting Rules for Friendly URLs

# إعادة توجيه الصفحة الرئيسية
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^admin/?$ admin/dashboard.php [L]

RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^customer/?$ customer/dashboard.php [L]

# حماية من الهجمات الشائعة
# Protection from Common Attacks

# منع SQL Injection في URL
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*iframe.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*object.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (\<|%3C).*embed.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (SELECT|UNION|INSERT|DROP|DELETE|UPDATE|CREATE|ALTER|EXEC) [NC]
RewriteRule .* - [F,L]

# منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# إعدادات MIME Types
# MIME Types Settings

<IfModule mod_mime.c>
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType application/vnd.ms-fontobject .eot
    AddType font/truetype .ttf
    AddType font/opentype .otf
</IfModule>

# صفحات الأخطاء المخصصة
# Custom Error Pages

ErrorDocument 404 /404.html
ErrorDocument 403 /403.html
ErrorDocument 500 /500.html

# تحسين الأداء
# Performance Optimization

<IfModule mod_rewrite.c>
    # إزالة الشرطة المائلة الزائدة
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.*)/$ /$1 [R=301,L]
</IfModule>

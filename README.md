# Meed Ps Workspace Management System
## نظام إدارة العملاء والخدمات - Meed Ps

نظام شامل لإدارة العملاء والخدمات التقنية مع إمكانية إنشاء الفواتير، مصمم خصيصاً لشركة Meed Ps لإدارة خدماتها التقنية المتميزة.

## 🌟 المميزات الرئيسية

### للعملاء:
- ✅ تسجيل حساب جديد وتسجيل الدخول
- ✅ لوحة تحكم شخصية تعرض:
  - الاشتراكات النشطة
  - تواريخ البداية والانتهاء
  - الأسعار والتجديد
  - نوع الخدمة والملاحظات
- ✅ عرض الفواتير وتحميلها
- ✅ تتبع حالة الخدمات

### للإدارة:
- ✅ لوحة تحكم شاملة مع إحصائيات
- ✅ إدارة العملاء (إضافة، تعديل، عرض)
- ✅ إدارة الخطط والأسعار
- ✅ إدارة الاشتراكات
- ✅ إنشاء وإدارة الفواتير
- ✅ تصدير الفواتير بصيغة PDF
- ✅ تقارير مالية وإحصائيات

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript
- **Styling**: CSS مخصص مع خطوط عربية (Cairo, Tajawal)
- **Security**: Password hashing, SQL injection protection, XSS protection

## 📋 متطلبات التشغيل

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx web server
- PDO MySQL extension

## 🚀 طريقة التثبيت

### 1. تحميل الملفات
```bash
git clone [repository-url]
cd workspace-management
```

### 2. إعداد قاعدة البيانات
1. استخدم قاعدة البيانات الموجودة في الاستضافة
2. استيراد هيكل قاعدة البيانات عبر phpMyAdmin:
   - اذهب إلى phpMyAdmin
   - اختر قاعدة البيانات
   - انسخ والصق محتوى ملف `database.sql`
   - اضغط Go

### 3. تكوين الاتصال بقاعدة البيانات
عدّل ملف `config/settings.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'meedpsco_hostmeed');
define('DB_USER', 'meedpsco_hostmeed');
define('DB_PASS', 'meedpsco_hostmeed');
```

### 4. رفع الملفات للخادم
ارفع جميع الملفات إلى مجلد الموقع على الخادم.

### 5. تعيين الصلاحيات
```bash
chmod 755 assets/
chmod 644 assets/css/*
chmod 644 assets/js/*
```

## 👤 بيانات الدخول الافتراضية

### الإدمن:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: password

> ⚠️ **مهم**: يرجى تغيير كلمة مرور الإدمن فور تسجيل الدخول الأول!

## 📁 هيكل المشروع

```
workspace-management/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── assets/
│   ├── css/
│   │   └── style.css         # ملف التصميم الرئيسي
│   └── js/
│       └── main.js           # ملف JavaScript الرئيسي
├── admin/                    # لوحة تحكم الإدارة
│   ├── dashboard.php         # الصفحة الرئيسية للإدارة
│   ├── customers.php         # إدارة العملاء
│   ├── plans.php             # إدارة الخطط
│   ├── invoices.php          # إدارة الفواتير
│   └── ...
├── customer/                 # لوحة تحكم العملاء
│   ├── dashboard.php         # الصفحة الرئيسية للعميل
│   └── logout.php            # تسجيل الخروج
├── includes/
│   └── invoice_pdf.php       # مولد فواتير PDF
├── index.php                 # صفحة تسجيل الدخول
├── register.php              # صفحة التسجيل
├── database.sql              # هيكل قاعدة البيانات
└── README.md                 # هذا الملف
```

## 🎨 التصميم والواجهة

- **تصميم متجاوب**: يعمل على جميع الأجهزة
- **خطوط عربية**: Cairo و Tajawal للنصوص العربية
- **ألوان احترافية**: تدرجات أزرق وبنفسجي
- **تأثيرات تفاعلية**: Hover effects وانتقالات سلسة
- **واجهة سهلة الاستخدام**: تصميم بديهي ومنظم

## 🔧 الإعدادات المتقدمة

### تخصيص الموقع
عدّل المتغيرات في `config/database.php`:
```php
define('SITE_URL', 'https://workspace.meedps.com');
define('SITE_NAME', 'Workspace Management');
define('ADMIN_EMAIL', '<EMAIL>');
```

### إعدادات الأمان
- تم تفعيل حماية CSRF
- تشفير كلمات المرور باستخدام password_hash()
- حماية من SQL Injection
- تنظيف البيانات المدخلة

## 📊 قاعدة البيانات

### الجداول الرئيسية:
- **users**: بيانات المستخدمين (عملاء وإدارة)
- **plans**: خطط الخدمات المتاحة
- **customer_subscriptions**: اشتراكات العملاء
- **invoices**: الفواتير والمدفوعات

## 🔄 التحديثات المستقبلية

- [ ] نظام إشعارات البريد الإلكتروني
- [ ] تقارير مالية متقدمة
- [ ] API للتكامل مع أنظمة أخرى
- [ ] نظام دفع إلكتروني
- [ ] تطبيق موبايل

## 🐛 الإبلاغ عن المشاكل

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى التواصل عبر:
- البريد الإلكتروني: <EMAIL>

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 🙏 شكر وتقدير

تم تطوير هذا النظام باستخدام أفضل الممارسات في البرمجة والأمان، مع التركيز على سهولة الاستخدام والأداء العالي.

---

**تم التطوير لشركة**: Meed Ps
**الموقع الرسمي**: https://meedps.com
**التاريخ**: 2025
**الإصدار**: 1.0.0

/**
 * ملف JavaScript الرئيسي للموقع
 * Main JavaScript file for the website
 */

// تأثيرات التحميل والتفاعل
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأثير fade-in للعناصر
    const elements = document.querySelectorAll('.fade-in');
    elements.forEach((element, index) => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            element.style.transition = 'all 0.6s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // تأثيرات الأزرار
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
    
    // تأثيرات الكروت
    const cards = document.querySelectorAll('.stat-card, .login-card, .table-container');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
        });
    });
});

/**
 * دالة لإظهار رسائل التنبيه
 */
function showAlert(message, type = 'info', duration = 5000) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.style.position = 'fixed';
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.style.minWidth = '300px';
    alertDiv.style.animation = 'slideInRight 0.5s ease';
    alertDiv.innerHTML = message;
    
    document.body.appendChild(alertDiv);
    
    setTimeout(() => {
        alertDiv.style.animation = 'slideOutRight 0.5s ease';
        setTimeout(() => {
            document.body.removeChild(alertDiv);
        }, 500);
    }, duration);
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

/**
 * دالة للتحقق من قوة كلمة المرور
 */
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];
    
    if (password.length >= 8) {
        strength += 1;
    } else {
        feedback.push('يجب أن تحتوي على 8 أحرف على الأقل');
    }
    
    if (password.match(/[a-z]/)) {
        strength += 1;
    } else {
        feedback.push('يجب أن تحتوي على حرف صغير');
    }
    
    if (password.match(/[A-Z]/)) {
        strength += 1;
    } else {
        feedback.push('يجب أن تحتوي على حرف كبير');
    }
    
    if (password.match(/[0-9]/)) {
        strength += 1;
    } else {
        feedback.push('يجب أن تحتوي على رقم');
    }
    
    if (password.match(/[^a-zA-Z0-9]/)) {
        strength += 1;
    } else {
        feedback.push('يجب أن تحتوي على رمز خاص');
    }
    
    return {
        strength: strength,
        feedback: feedback,
        level: strength < 2 ? 'ضعيف' : strength < 4 ? 'متوسط' : 'قوي'
    };
}

/**
 * دالة لتنسيق التواريخ
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    const options = { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric',
        weekday: 'long'
    };
    return date.toLocaleDateString('ar-SA', options);
}

/**
 * دالة لتنسيق الأرقام والعملة
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: 'SAR'
    }).format(amount);
}

/**
 * دالة لإرسال طلبات AJAX
 */
function sendAjaxRequest(url, data, method = 'POST') {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(method, url, true);
        xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new Error('خطأ في الشبكة'));
                }
            }
        };
        
        if (method === 'POST' && data) {
            const formData = new URLSearchParams(data);
            xhr.send(formData);
        } else {
            xhr.send();
        }
    });
}

/**
 * دالة لتأكيد الحذف
 */
function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
    return confirm(message);
}

/**
 * دالة لإظهار/إخفاء كلمة المرور
 */
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
    input.setAttribute('type', type);
}

/**
 * دالة للبحث في الجداول
 */
function searchTable(inputId, tableId) {
    const input = document.getElementById(inputId);
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');
    
    input.addEventListener('keyup', function() {
        const filter = this.value.toLowerCase();
        
        for (let i = 1; i < rows.length; i++) {
            const row = rows[i];
            const cells = row.getElementsByTagName('td');
            let found = false;
            
            for (let j = 0; j < cells.length; j++) {
                const cell = cells[j];
                if (cell.textContent.toLowerCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
            
            row.style.display = found ? '' : 'none';
        }
    });
}

/**
 * دالة لتحديث الوقت الحالي
 */
function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('ar-SA');
    const dateString = now.toLocaleDateString('ar-SA');
    
    const timeElement = document.getElementById('current-time');
    if (timeElement) {
        timeElement.textContent = `${timeString} - ${dateString}`;
    }
}

// تحديث الوقت كل ثانية
setInterval(updateCurrentTime, 1000);

/**
 * دالة لحفظ البيانات محلياً
 */
function saveToLocalStorage(key, data) {
    try {
        localStorage.setItem(key, JSON.stringify(data));
        return true;
    } catch (e) {
        console.error('خطأ في حفظ البيانات محلياً:', e);
        return false;
    }
}

/**
 * دالة لاسترجاع البيانات المحفوظة محلياً
 */
function getFromLocalStorage(key) {
    try {
        const data = localStorage.getItem(key);
        return data ? JSON.parse(data) : null;
    } catch (e) {
        console.error('خطأ في استرجاع البيانات المحلية:', e);
        return null;
    }
}

// إضافة تأثيرات CSS للحركات
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    .smooth-transition {
        transition: all 0.3s ease;
    }
`;
document.head.appendChild(style);

<?php
session_start();
require_once 'config/database.php';

// إذا كان المستخدم مسجل دخول، إعادة توجيه
if (isLoggedIn()) {
    if (isAdmin()) {
        redirect('admin/dashboard.php');
    } else {
        redirect('customer/dashboard.php');
    }
}

$error_message = '';
$success_message = '';

// معالجة تسجيل حساب جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['register'])) {
    $username = sanitize($_POST['username']);
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $full_name = sanitize($_POST['full_name']);
    $phone = sanitize($_POST['phone']);
    
    // التحقق من البيانات
    if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (strlen($password) < 6) {
        $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error_message = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } else {
        try {
            $database = new Database();
            $db = $database->getConnection();
            
            // التحقق من عدم وجود المستخدم مسبقاً
            $check_query = "SELECT id FROM users WHERE email = ? OR username = ?";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->execute([$email, $username]);
            
            if ($check_stmt->rowCount() > 0) {
                $error_message = 'البريد الإلكتروني أو اسم المستخدم مستخدم مسبقاً';
            } else {
                // إنشاء الحساب الجديد
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                $insert_query = "INSERT INTO users (username, email, password, full_name, phone, user_type) VALUES (?, ?, ?, ?, ?, 'customer')";
                $insert_stmt = $db->prepare($insert_query);
                
                if ($insert_stmt->execute([$username, $email, $hashed_password, $full_name, $phone])) {
                    $success_message = 'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول';
                    
                    // مسح البيانات من النموذج
                    $_POST = array();
                } else {
                    $error_message = 'حدث خطأ أثناء إنشاء الحساب';
                }
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ في النظام: ' . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - Meed Ps Workspace</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <div class="login-container">
        <div class="login-card fade-in" style="max-width: 500px;">
            <div class="login-header">
                <h1>انضم إلى Meed Ps</h1>
                <p>أنشئ حسابك واستمتع بخدماتنا التقنية المتميزة</p>
            </div>
            
            <?php if ($error_message): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <form method="POST" action="" id="registerForm">
                <div class="form-group">
                    <label for="full_name">الاسم الكامل *</label>
                    <input type="text" id="full_name" name="full_name" class="form-control" 
                           placeholder="أدخل اسمك الكامل" required 
                           value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="username">اسم المستخدم *</label>
                    <input type="text" id="username" name="username" class="form-control" 
                           placeholder="اختر اسم مستخدم فريد" required 
                           value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="email">البريد الإلكتروني *</label>
                    <input type="email" id="email" name="email" class="form-control" 
                           placeholder="أدخل بريدك الإلكتروني" required 
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="phone">رقم الهاتف</label>
                    <input type="tel" id="phone" name="phone" class="form-control" 
                           placeholder="أدخل رقم هاتفك" 
                           value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور *</label>
                    <input type="password" id="password" name="password" class="form-control" 
                           placeholder="أدخل كلمة مرور قوية (6 أحرف على الأقل)" required>
                    <small style="color: #666; font-size: 0.9rem;">يجب أن تحتوي على 6 أحرف على الأقل</small>
                </div>
                
                <div class="form-group">
                    <label for="confirm_password">تأكيد كلمة المرور *</label>
                    <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                           placeholder="أعد إدخال كلمة المرور" required>
                </div>
                
                <button type="submit" name="register" class="btn btn-primary">
                    <span class="btn-text">إنشاء الحساب</span>
                    <span class="loading" style="display: none;"></span>
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e1e5e9;">
                <p style="color: #666; margin-bottom: 1rem;">لديك حساب بالفعل؟</p>
                <a href="index.php" class="btn btn-secondary">تسجيل الدخول</a>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        // التحقق من تطابق كلمات المرور
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword && confirmPassword.length > 0) {
                this.style.borderColor = '#dc3545';
                this.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';
            } else {
                this.style.borderColor = '#e1e5e9';
                this.style.boxShadow = 'none';
            }
        });
        
        // تأثيرات النموذج
        document.getElementById('registerForm').addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return;
            }
            
            const btnText = document.querySelector('.btn-text');
            const loading = document.querySelector('.loading');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            btnText.style.display = 'none';
            loading.style.display = 'inline-block';
            submitBtn.disabled = true;
        });
        
        // التحقق من قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strength = getPasswordStrength(password);
            
            // يمكن إضافة مؤشر قوة كلمة المرور هنا
        });
        
        function getPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            return strength;
        }
    </script>
</body>
</html>

<?php
require_once '../config/database.php';

/**
 * فئة إنشاء فواتير PDF
 * PDF Invoice Generator Class
 */
class InvoicePDF {
    private $invoice_data;
    private $customer_data;
    private $subscription_data;
    
    public function __construct($invoice_id) {
        $this->loadInvoiceData($invoice_id);
    }
    
    private function loadInvoiceData($invoice_id) {
        $database = new Database();
        $db = $database->getConnection();
        
        // جلب بيانات الفاتورة مع بيانات العميل والاشتراك
        $query = "
            SELECT i.*, 
                   u.full_name, u.email, u.phone,
                   cs.subscription_name, cs.service_type, cs.start_date, cs.end_date,
                   p.plan_name
            FROM invoices i
            JOIN users u ON i.customer_id = u.id
            LEFT JOIN customer_subscriptions cs ON i.subscription_id = cs.id
            LEFT JOIN plans p ON cs.plan_id = p.id
            WHERE i.id = ?
        ";
        
        $stmt = $db->prepare($query);
        $stmt->execute([$invoice_id]);
        $data = $stmt->fetch();
        
        if (!$data) {
            throw new Exception('الفاتورة غير موجودة');
        }
        
        $this->invoice_data = $data;
    }
    
    public function generatePDF() {
        // إنشاء محتوى HTML للفاتورة
        $html = $this->generateHTMLContent();
        
        // تحويل HTML إلى PDF باستخدام مكتبة بسيطة
        return $this->convertHTMLToPDF($html);
    }
    
    private function generateHTMLContent() {
        $invoice = $this->invoice_data;
        
        $html = '
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                @import url("https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap");
                
                body {
                    font-family: "Cairo", Arial, sans-serif;
                    direction: rtl;
                    text-align: right;
                    margin: 0;
                    padding: 20px;
                    color: #333;
                    line-height: 1.6;
                }
                
                .invoice-container {
                    max-width: 800px;
                    margin: 0 auto;
                    background: white;
                    border: 1px solid #ddd;
                    border-radius: 10px;
                    overflow: hidden;
                }
                
                .invoice-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }
                
                .company-name {
                    font-size: 28px;
                    font-weight: 700;
                    margin-bottom: 10px;
                }
                
                .invoice-title {
                    font-size: 20px;
                    font-weight: 500;
                }
                
                .invoice-body {
                    padding: 30px;
                }
                
                .invoice-info {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 30px;
                    flex-wrap: wrap;
                }
                
                .info-section {
                    flex: 1;
                    min-width: 250px;
                    margin-bottom: 20px;
                }
                
                .info-title {
                    font-size: 16px;
                    font-weight: 600;
                    color: #667eea;
                    margin-bottom: 10px;
                    border-bottom: 2px solid #667eea;
                    padding-bottom: 5px;
                }
                
                .info-content {
                    font-size: 14px;
                    line-height: 1.8;
                }
                
                .invoice-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 30px 0;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    overflow: hidden;
                }
                
                .invoice-table th {
                    background: #f8f9fa;
                    padding: 15px;
                    text-align: right;
                    font-weight: 600;
                    border-bottom: 1px solid #ddd;
                }
                
                .invoice-table td {
                    padding: 15px;
                    border-bottom: 1px solid #eee;
                }
                
                .total-section {
                    background: #f8f9fa;
                    padding: 20px;
                    border-radius: 8px;
                    margin-top: 30px;
                }
                
                .total-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    font-size: 16px;
                }
                
                .total-final {
                    font-size: 20px;
                    font-weight: 700;
                    color: #667eea;
                    border-top: 2px solid #667eea;
                    padding-top: 15px;
                    margin-top: 15px;
                }
                
                .invoice-footer {
                    background: #f8f9fa;
                    padding: 20px 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #666;
                    border-top: 1px solid #ddd;
                }
                
                .status-badge {
                    display: inline-block;
                    padding: 5px 15px;
                    border-radius: 20px;
                    font-size: 12px;
                    font-weight: 600;
                    text-transform: uppercase;
                }
                
                .status-paid {
                    background: #d4edda;
                    color: #155724;
                }
                
                .status-pending {
                    background: #fff3cd;
                    color: #856404;
                }
                
                .status-overdue {
                    background: #f8d7da;
                    color: #721c24;
                }
                
                @media print {
                    body { margin: 0; padding: 0; }
                    .invoice-container { border: none; border-radius: 0; }
                }
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <!-- رأس الفاتورة -->
                <div class="invoice-header">
                    <div class="company-name">Meed Ps</div>
                    <div class="invoice-title">فاتورة رقم: ' . htmlspecialchars($invoice['invoice_number']) . '</div>
                </div>
                
                <!-- محتوى الفاتورة -->
                <div class="invoice-body">
                    <!-- معلومات الفاتورة -->
                    <div class="invoice-info">
                        <div class="info-section">
                            <div class="info-title">بيانات العميل</div>
                            <div class="info-content">
                                <strong>' . htmlspecialchars($invoice['full_name']) . '</strong><br>
                                البريد الإلكتروني: ' . htmlspecialchars($invoice['email']) . '<br>';
                                
        if ($invoice['phone']) {
            $html .= 'الهاتف: ' . htmlspecialchars($invoice['phone']) . '<br>';
        }
        
        $html .= '
                            </div>
                        </div>
                        
                        <div class="info-section">
                            <div class="info-title">تفاصيل الفاتورة</div>
                            <div class="info-content">
                                رقم الفاتورة: <strong>' . htmlspecialchars($invoice['invoice_number']) . '</strong><br>
                                تاريخ الإصدار: ' . date('Y/m/d', strtotime($invoice['invoice_date'])) . '<br>
                                تاريخ الاستحقاق: ' . date('Y/m/d', strtotime($invoice['due_date'])) . '<br>
                                الحالة: ';
                                
        $status_class = '';
        $status_text = '';
        switch ($invoice['status']) {
            case 'paid':
                $status_class = 'status-paid';
                $status_text = 'مدفوع';
                break;
            case 'pending':
                $status_class = 'status-pending';
                $status_text = 'معلق';
                break;
            case 'overdue':
                $status_class = 'status-overdue';
                $status_text = 'متأخر';
                break;
        }
        
        $html .= '<span class="status-badge ' . $status_class . '">' . $status_text . '</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- جدول الخدمات -->
                    <table class="invoice-table">
                        <thead>
                            <tr>
                                <th>الخدمة</th>
                                <th>النوع</th>
                                <th>الفترة</th>
                                <th>المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>' . htmlspecialchars($invoice['subscription_name'] ?: $invoice['plan_name']) . '</strong></td>
                                <td>' . htmlspecialchars($invoice['service_type'] ?: 'خدمة عامة') . '</td>
                                <td>';
                                
        if ($invoice['start_date'] && $invoice['end_date']) {
            $html .= date('Y/m/d', strtotime($invoice['start_date'])) . ' - ' . date('Y/m/d', strtotime($invoice['end_date']));
        } else {
            $html .= 'غير محدد';
        }
        
        $html .= '</td>
                                <td><strong>' . number_format($invoice['amount'], 2) . ' ₪</strong></td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <!-- إجمالي المبلغ -->
                    <div class="total-section">
                        <div class="total-row">
                            <span>المبلغ الأساسي:</span>
                            <span>' . number_format($invoice['amount'], 2) . ' ₪</span>
                        </div>
                        <div class="total-row">
                            <span>ضريبة القيمة المضافة (16%):</span>
                            <span>' . number_format($invoice['amount'] * 0.16, 2) . ' ₪</span>
                        </div>
                        <div class="total-row total-final">
                            <span>المبلغ الإجمالي:</span>
                            <span>' . number_format($invoice['amount'] * 1.16, 2) . ' ₪</span>
                        </div>
                    </div>';
                    
        if ($invoice['notes']) {
            $html .= '
                    <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                        <div class="info-title">ملاحظات</div>
                        <div class="info-content">' . nl2br(htmlspecialchars($invoice['notes'])) . '</div>
                    </div>';
        }
        
        $html .= '
                </div>
                
                <!-- تذييل الفاتورة -->
                <div class="invoice-footer">
                    <p><strong>Meed Ps</strong> - تقنية بمعايير عالمية من قلب فلسطين</p>
                    <p>للاستفسارات: ' . ADMIN_EMAIL . ' | الموقع: https://meedps.com</p>
                    <p>تم إنشاء هذه الفاتورة تلقائياً في ' . date('Y/m/d H:i') . '</p>
                </div>
            </div>
        </body>
        </html>';
        
        return $html;
    }
    
    private function convertHTMLToPDF($html) {
        // في بيئة الإنتاج، يمكن استخدام مكتبات مثل TCPDF أو DomPDF
        // هنا سنقوم بإرجاع HTML مباشرة للعرض في المتصفح
        return $html;
    }
    
    public function downloadPDF() {
        $html = $this->generateHTMLContent();
        
        // تعيين headers للتحميل
        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: inline; filename="invoice_' . $this->invoice_data['invoice_number'] . '.html"');
        
        return $html;
    }
    
    public function getInvoiceData() {
        return $this->invoice_data;
    }
}

/**
 * دالة مساعدة لإنشاء رقم فاتورة فريد
 */
function generateInvoiceNumber() {
    return 'INV-' . date('Y') . '-' . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
}

/**
 * دالة لإنشاء فاتورة جديدة
 */
function createInvoice($subscription_id, $customer_id, $amount, $due_date, $notes = '') {
    $database = new Database();
    $db = $database->getConnection();
    
    $invoice_number = generateInvoiceNumber();
    
    // التأكد من عدم تكرار رقم الفاتورة
    $check_query = "SELECT id FROM invoices WHERE invoice_number = ?";
    $check_stmt = $db->prepare($check_query);
    $check_stmt->execute([$invoice_number]);
    
    while ($check_stmt->rowCount() > 0) {
        $invoice_number = generateInvoiceNumber();
        $check_stmt->execute([$invoice_number]);
    }
    
    $insert_query = "
        INSERT INTO invoices (subscription_id, customer_id, invoice_number, amount, invoice_date, due_date, notes) 
        VALUES (?, ?, ?, ?, CURDATE(), ?, ?)
    ";
    
    $insert_stmt = $db->prepare($insert_query);
    
    if ($insert_stmt->execute([$subscription_id, $customer_id, $invoice_number, $amount, $due_date, $notes])) {
        return $db->lastInsertId();
    }
    
    return false;
}
?>

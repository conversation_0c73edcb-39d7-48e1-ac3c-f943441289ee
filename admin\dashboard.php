<?php
session_start();
require_once '../config/database.php';

// التحقق من صلاحيات الإدمن
if (!isLoggedIn() || !isAdmin()) {
    header("Location: ../index.php");
    exit();
}

$database = new Database();
$db = $database->getConnection();

// جلب الإحصائيات
$stats = [];

// عدد العملاء
$customers_query = "SELECT COUNT(*) as count FROM users WHERE user_type = 'customer'";
$customers_stmt = $db->prepare($customers_query);
$customers_stmt->execute();
$stats['customers'] = $customers_stmt->fetch()['count'];

// عدد الاشتراكات النشطة
$active_subs_query = "SELECT COUNT(*) as count FROM customer_subscriptions WHERE status = 'active'";
$active_subs_stmt = $db->prepare($active_subs_query);
$active_subs_stmt->execute();
$stats['active_subscriptions'] = $active_subs_stmt->fetch()['count'];

// إجمالي الإيرادات
$revenue_query = "SELECT SUM(amount) as total FROM invoices WHERE status = 'paid'";
$revenue_stmt = $db->prepare($revenue_query);
$revenue_stmt->execute();
$stats['total_revenue'] = $revenue_stmt->fetch()['total'] ?: 0;

// الفواتير المعلقة
$pending_invoices_query = "SELECT COUNT(*) as count FROM invoices WHERE status = 'pending'";
$pending_invoices_stmt = $db->prepare($pending_invoices_query);
$pending_invoices_stmt->execute();
$stats['pending_invoices'] = $pending_invoices_stmt->fetch()['count'];

// جلب آخر العملاء المسجلين
$recent_customers_query = "
    SELECT id, username, full_name, email, created_at 
    FROM users 
    WHERE user_type = 'customer' 
    ORDER BY created_at DESC 
    LIMIT 5
";
$recent_customers_stmt = $db->prepare($recent_customers_query);
$recent_customers_stmt->execute();
$recent_customers = $recent_customers_stmt->fetchAll();

// جلب الاشتراكات المنتهية قريباً
$expiring_subs_query = "
    SELECT cs.*, u.full_name, u.email 
    FROM customer_subscriptions cs 
    JOIN users u ON cs.customer_id = u.id 
    WHERE cs.status = 'active' 
    AND cs.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
    ORDER BY cs.end_date ASC 
    LIMIT 5
";
$expiring_subs_stmt = $db->prepare($expiring_subs_query);
$expiring_subs_stmt->execute();
$expiring_subscriptions = $expiring_subs_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإدارة - Meed Ps Workspace</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="#" class="logo">Meed Ps - إدارة العملاء</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php">العملاء</a></li>
                    <li><a href="subscriptions.php">الاشتراكات</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="plans.php">الخطط</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- ترحيب -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">لوحة تحكم Meed Ps</h1>
                <p class="dashboard-subtitle">مرحباً <?php echo htmlspecialchars($_SESSION['full_name']); ?>، إليك نظرة شاملة على العملاء والخدمات</p>
                <div style="margin-top: 1rem;">
                    <small style="color: #666;" id="current-time"></small>
                </div>
            </div>

            <!-- الإحصائيات الرئيسية -->
            <div class="stats-grid fade-in">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['customers']; ?></div>
                    <div class="stat-label">إجمالي العملاء</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active_subscriptions']; ?></div>
                    <div class="stat-label">الاشتراكات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_revenue'], 2); ?> ₪</div>
                    <div class="stat-label">إجمالي الإيرادات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['pending_invoices']; ?></div>
                    <div class="stat-label">الفواتير المعلقة</div>
                </div>
            </div>

            <!-- الإجراءات السريعة -->
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 1.5rem; color: #333;">الإجراءات السريعة</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                    <a href="add_customer.php" class="btn btn-primary" style="text-decoration: none; display: block; text-align: center;">
                        إضافة عميل جديد
                    </a>
                    <a href="add_subscription.php" class="btn btn-primary" style="text-decoration: none; display: block; text-align: center;">
                        إضافة اشتراك
                    </a>
                    <a href="create_invoice.php" class="btn btn-primary" style="text-decoration: none; display: block; text-align: center;">
                        إنشاء فاتورة
                    </a>
                    <a href="add_plan.php" class="btn btn-primary" style="text-decoration: none; display: block; text-align: center;">
                        إضافة خطة جديدة
                    </a>
                </div>
            </div>

            <!-- آخر العملاء المسجلين -->
            <?php if (!empty($recent_customers)): ?>
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 1.5rem; color: #333;">آخر العملاء المسجلين</h2>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>تاريخ التسجيل</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_customers as $customer): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($customer['full_name']); ?></strong></td>
                                <td><?php echo htmlspecialchars($customer['username']); ?></td>
                                <td><?php echo htmlspecialchars($customer['email']); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($customer['created_at'])); ?></td>
                                <td>
                                    <a href="customer_details.php?id=<?php echo $customer['id']; ?>" 
                                       class="btn btn-sm btn-primary">عرض التفاصيل</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <div style="text-align: center; margin-top: 1.5rem;">
                    <a href="customers.php" class="btn btn-secondary">عرض جميع العملاء</a>
                </div>
            </div>
            <?php endif; ?>

            <!-- الاشتراكات المنتهية قريباً -->
            <?php if (!empty($expiring_subscriptions)): ?>
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 1.5rem; color: #333;">اشتراكات تنتهي خلال 30 يوم</h2>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>العميل</th>
                                <th>اسم الاشتراك</th>
                                <th>تاريخ الانتهاء</th>
                                <th>المبلغ</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($expiring_subscriptions as $subscription): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($subscription['full_name']); ?></strong><br>
                                    <small style="color: #666;"><?php echo htmlspecialchars($subscription['email']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($subscription['subscription_name']); ?></td>
                                <td>
                                    <?php 
                                    $end_date = new DateTime($subscription['end_date']);
                                    $now = new DateTime();
                                    $diff = $now->diff($end_date);
                                    echo date('Y/m/d', strtotime($subscription['end_date']));
                                    echo '<br><small style="color: #dc3545;">(' . $diff->days . ' يوم متبقي)</small>';
                                    ?>
                                </td>
                                <td><?php echo number_format($subscription['renewal_price'], 2); ?> ₪</td>
                                <td>
                                    <a href="renew_subscription.php?id=<?php echo $subscription['id']; ?>" 
                                       class="btn btn-sm btn-primary">تجديد</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        // تحديث الإحصائيات كل 30 ثانية
        setInterval(function() {
            // يمكن إضافة AJAX لتحديث الإحصائيات تلقائياً
        }, 30000);
        
        // تأثيرات إضافية للوحة الإدارة
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات للأزرار
            const actionButtons = document.querySelectorAll('.btn');
            actionButtons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px) scale(1.02)';
                });
                
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                });
            });
        });
    </script>
</body>
</html>

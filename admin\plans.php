<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || $_SESSION['user_type'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة حذف خطة
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $plan_id = (int)$_GET['delete'];
    
    try {
        // التحقق من عدم وجود اشتراكات مرتبطة بالخطة
        $check_query = "SELECT COUNT(*) as count FROM customer_subscriptions WHERE plan_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$plan_id]);
        $subscription_count = $check_stmt->fetch()['count'];
        
        if ($subscription_count > 0) {
            $error_message = 'لا يمكن حذف هذه الخطة لأنها مرتبطة باشتراكات موجودة';
        } else {
            $delete_query = "DELETE FROM plans WHERE id = ?";
            $delete_stmt = $db->prepare($delete_query);
            
            if ($delete_stmt->execute([$plan_id])) {
                $success_message = 'تم حذف الخطة بنجاح';
            } else {
                $error_message = 'حدث خطأ أثناء حذف الخطة';
            }
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ في النظام';
    }
}

// جلب جميع الخطط مع إحصائياتها
$plans_query = "
    SELECT p.*, 
           COUNT(cs.id) as total_subscriptions,
           COUNT(CASE WHEN cs.status = 'active' THEN cs.id END) as active_subscriptions
    FROM plans p 
    LEFT JOIN customer_subscriptions cs ON p.id = cs.plan_id 
    GROUP BY p.id 
    ORDER BY p.created_at DESC
";

$plans_stmt = $db->prepare($plans_query);
$plans_stmt->execute();
$plans = $plans_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الخطط - Workspace Management</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="dashboard.php" class="logo">Workspace Management - إدارة</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php">العملاء</a></li>
                    <li><a href="subscriptions.php">الاشتراكات</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="plans.php" style="color: #667eea;">الخطط</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">إدارة الخطط</h1>
                <p class="dashboard-subtitle">عرض وإدارة جميع خطط الخدمات المتاحة</p>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success fade-in"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger fade-in"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- أدوات الإضافة -->
            <div class="table-container fade-in">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem;">
                    <h2 style="color: #333; margin: 0;">الخطط المتاحة</h2>
                    <a href="add_plan.php" class="btn btn-primary">إضافة خطة جديدة</a>
                </div>

                <!-- جدول الخطط -->
                <?php if (!empty($plans)): ?>
                <div style="overflow-x: auto;">
                    <table class="table" id="plansTable">
                        <thead>
                            <tr>
                                <th>اسم الخطة</th>
                                <th>الوصف</th>
                                <th>السعر الأساسي</th>
                                <th>سعر التجديد</th>
                                <th>المدة (شهر)</th>
                                <th>الاشتراكات</th>
                                <th>الحالة</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($plans as $plan): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($plan['plan_name']); ?></strong>
                                </td>
                                <td>
                                    <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                                        <?php echo htmlspecialchars(substr($plan['description'], 0, 100)); ?>
                                        <?php if (strlen($plan['description']) > 100): ?>...<?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <strong style="color: #667eea;">
                                        <?php echo number_format($plan['base_price'], 2); ?> ر.س
                                    </strong>
                                </td>
                                <td>
                                    <strong style="color: #28a745;">
                                        <?php echo number_format($plan['renewal_price'], 2); ?> ر.س
                                    </strong>
                                </td>
                                <td><?php echo $plan['duration_months']; ?> شهر</td>
                                <td>
                                    <span style="color: #667eea; font-weight: 600;">
                                        <?php echo $plan['active_subscriptions']; ?>
                                    </span>
                                    /
                                    <?php echo $plan['total_subscriptions']; ?>
                                </td>
                                <td>
                                    <?php if ($plan['status'] === 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo date('Y/m/d', strtotime($plan['created_at'])); ?></td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                        <a href="edit_plan.php?id=<?php echo $plan['id']; ?>" 
                                           class="btn btn-sm btn-secondary">تعديل</a>
                                        <a href="plan_details.php?id=<?php echo $plan['id']; ?>" 
                                           class="btn btn-sm btn-primary">التفاصيل</a>
                                        <?php if ($plan['total_subscriptions'] == 0): ?>
                                            <a href="?delete=<?php echo $plan['id']; ?>" 
                                               class="btn btn-sm" style="background: #dc3545; color: white;"
                                               onclick="return confirm('هل أنت متأكد من حذف هذه الخطة؟')">حذف</a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <h3>لا توجد خطط مضافة حالياً</h3>
                    <p>ابدأ بإضافة خطة جديدة</p>
                    <a href="add_plan.php" class="btn btn-primary">إضافة خطة جديدة</a>
                </div>
                <?php endif; ?>
            </div>

            <!-- إحصائيات سريعة -->
            <?php if (!empty($plans)): ?>
            <div class="stats-grid fade-in">
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($plans); ?></div>
                    <div class="stat-label">إجمالي الخطط</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        <?php echo count(array_filter($plans, function($p) { return $p['status'] === 'active'; })); ?>
                    </div>
                    <div class="stat-label">الخطط النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        <?php echo array_sum(array_column($plans, 'active_subscriptions')); ?>
                    </div>
                    <div class="stat-label">الاشتراكات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">
                        <?php echo number_format(array_sum(array_column($plans, 'base_price')) / count($plans), 2); ?> ر.س
                    </div>
                    <div class="stat-label">متوسط سعر الخطط</div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        // إضافة تأثيرات للجدول
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات الصفوف
            const rows = document.querySelectorAll('#plansTable tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(102, 126, 234, 0.05)';
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'all 0.2s ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });
            
            // تأثيرات الشارات
            const badges = document.querySelectorAll('.badge');
            badges.forEach(badge => {
                badge.style.padding = '0.25rem 0.75rem';
                badge.style.borderRadius = '20px';
                badge.style.fontSize = '0.875rem';
                badge.style.fontWeight = '500';
                
                if (badge.classList.contains('badge-success')) {
                    badge.style.background = '#d4edda';
                    badge.style.color = '#155724';
                } else if (badge.classList.contains('badge-danger')) {
                    badge.style.background = '#f8d7da';
                    badge.style.color = '#721c24';
                }
            });
        });
    </script>
</body>
</html>

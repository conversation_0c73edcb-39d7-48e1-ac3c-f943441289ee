<?php
session_start();
require_once '../config/database.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn() || isAdmin()) {
    header("Location: ../index.php");
    exit();
}

$database = new Database();
$db = $database->getConnection();

// جلب بيانات العميل
$customer_id = $_SESSION['user_id'];

// جلب اشتراكات العميل
$subscriptions_query = "
    SELECT cs.*, p.plan_name, p.description as plan_description 
    FROM customer_subscriptions cs 
    LEFT JOIN plans p ON cs.plan_id = p.id 
    WHERE cs.customer_id = ? 
    ORDER BY cs.created_at DESC
";
$subscriptions_stmt = $db->prepare($subscriptions_query);
$subscriptions_stmt->execute([$customer_id]);
$subscriptions = $subscriptions_stmt->fetchAll();

// جلب الفواتير
$invoices_query = "
    SELECT i.*, cs.subscription_name 
    FROM invoices i 
    LEFT JOIN customer_subscriptions cs ON i.subscription_id = cs.id 
    WHERE i.customer_id = ? 
    ORDER BY i.created_at DESC 
    LIMIT 10
";
$invoices_stmt = $db->prepare($invoices_query);
$invoices_stmt->execute([$customer_id]);
$invoices = $invoices_stmt->fetchAll();

// إحصائيات سريعة
$active_subscriptions = count(array_filter($subscriptions, function($sub) {
    return $sub['status'] === 'active';
}));

$total_spent = 0;
foreach ($invoices as $invoice) {
    if ($invoice['status'] === 'paid') {
        $total_spent += $invoice['amount'];
    }
}

$pending_invoices = count(array_filter($invoices, function($inv) {
    return $inv['status'] === 'pending';
}));
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo htmlspecialchars($_SESSION['full_name']); ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="#" class="logo">Meed Ps</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="subscriptions.php">اشتراكاتي</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="profile.php">الملف الشخصي</a></li>
                    <li><a href="logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- ترحيب -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">مرحباً، <?php echo htmlspecialchars($_SESSION['full_name']); ?></h1>
                <p class="dashboard-subtitle">إليك نظرة سريعة على خدماتك مع Meed Ps</p>
                <div style="margin-top: 1rem;">
                    <small style="color: #666;" id="current-time"></small>
                </div>
            </div>

            <!-- الإحصائيات -->
            <div class="stats-grid fade-in">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $active_subscriptions; ?></div>
                    <div class="stat-label">الاشتراكات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($total_spent, 2); ?> ₪</div>
                    <div class="stat-label">إجمالي المدفوعات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $pending_invoices; ?></div>
                    <div class="stat-label">الفواتير المعلقة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo count($subscriptions); ?></div>
                    <div class="stat-label">إجمالي الخدمات</div>
                </div>
            </div>

            <!-- الاشتراكات النشطة -->
            <?php if (!empty($subscriptions)): ?>
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 1.5rem; color: #333;">اشتراكاتك الحالية</h2>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الخطة</th>
                                <th>نوع الخدمة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ الانتهاء</th>
                                <th>السعر</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subscriptions as $subscription): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($subscription['subscription_name'] ?: $subscription['plan_name']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($subscription['service_type'] ?: 'غير محدد'); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($subscription['start_date'])); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($subscription['end_date'])); ?></td>
                                <td><?php echo number_format($subscription['subscription_price'], 2); ?> ر.س</td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    $status_text = '';
                                    switch ($subscription['status']) {
                                        case 'active':
                                            $status_class = 'success';
                                            $status_text = 'نشط';
                                            break;
                                        case 'expired':
                                            $status_class = 'danger';
                                            $status_text = 'منتهي';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'secondary';
                                            $status_text = 'ملغي';
                                            break;
                                    }
                                    ?>
                                    <span class="badge badge-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                </td>
                                <td>
                                    <a href="subscription_details.php?id=<?php echo $subscription['id']; ?>" 
                                       class="btn btn-sm btn-primary">التفاصيل</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
            <?php else: ?>
            <div class="table-container fade-in" style="text-align: center; padding: 3rem;">
                <h3 style="color: #666; margin-bottom: 1rem;">لا توجد اشتراكات حالياً</h3>
                <p style="color: #999; margin-bottom: 2rem;">تواصل مع فريق الدعم لإضافة خطة جديدة</p>
                <a href="mailto:<?php echo ADMIN_EMAIL; ?>" class="btn btn-primary">تواصل معنا</a>
            </div>
            <?php endif; ?>

            <!-- آخر الفواتير -->
            <?php if (!empty($invoices)): ?>
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 1.5rem; color: #333;">آخر الفواتير</h2>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>الخدمة</th>
                                <th>المبلغ</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach (array_slice($invoices, 0, 5) as $invoice): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong></td>
                                <td><?php echo htmlspecialchars($invoice['subscription_name'] ?: 'غير محدد'); ?></td>
                                <td><?php echo number_format($invoice['amount'], 2); ?> ر.س</td>
                                <td><?php echo date('Y/m/d', strtotime($invoice['invoice_date'])); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($invoice['due_date'])); ?></td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    $status_text = '';
                                    switch ($invoice['status']) {
                                        case 'paid':
                                            $status_class = 'success';
                                            $status_text = 'مدفوع';
                                            break;
                                        case 'pending':
                                            $status_class = 'warning';
                                            $status_text = 'معلق';
                                            break;
                                        case 'overdue':
                                            $status_class = 'danger';
                                            $status_text = 'متأخر';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'secondary';
                                            $status_text = 'ملغي';
                                            break;
                                    }
                                    ?>
                                    <span class="badge badge-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                </td>
                                <td>
                                    <a href="invoice_details.php?id=<?php echo $invoice['id']; ?>" 
                                       class="btn btn-sm btn-primary">عرض</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php if (count($invoices) > 5): ?>
                <div style="text-align: center; margin-top: 1.5rem;">
                    <a href="invoices.php" class="btn btn-secondary">عرض جميع الفواتير</a>
                </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        // إضافة تأثيرات للشارات
        document.addEventListener('DOMContentLoaded', function() {
            const badges = document.querySelectorAll('.badge');
            badges.forEach(badge => {
                badge.style.padding = '0.25rem 0.75rem';
                badge.style.borderRadius = '20px';
                badge.style.fontSize = '0.875rem';
                badge.style.fontWeight = '500';
                
                if (badge.classList.contains('badge-success')) {
                    badge.style.background = '#d4edda';
                    badge.style.color = '#155724';
                } else if (badge.classList.contains('badge-danger')) {
                    badge.style.background = '#f8d7da';
                    badge.style.color = '#721c24';
                } else if (badge.classList.contains('badge-warning')) {
                    badge.style.background = '#fff3cd';
                    badge.style.color = '#856404';
                } else if (badge.classList.contains('badge-secondary')) {
                    badge.style.background = '#e2e3e5';
                    badge.style.color = '#383d41';
                }
            });
        });
    </script>
</body>
</html>

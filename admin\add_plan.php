<?php
require_once '../config/database.php';

// التحقق من صلاحيات الإدمن
if (!isLoggedIn() || !isAdmin()) {
    redirect('../index.php');
}

$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة إضافة خطة جديدة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_plan'])) {
    $plan_name = sanitize($_POST['plan_name']);
    $description = sanitize($_POST['description']);
    $base_price = floatval($_POST['base_price']);
    $renewal_price = floatval($_POST['renewal_price']);
    $duration_months = intval($_POST['duration_months']);
    $features = sanitize($_POST['features']);
    $status = sanitize($_POST['status']);
    
    // التحقق من البيانات
    if (empty($plan_name) || empty($description) || $base_price <= 0 || $renewal_price <= 0 || $duration_months <= 0) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة بقيم صحيحة';
    } else {
        try {
            // التحقق من عدم وجود خطة بنفس الاسم
            $check_query = "SELECT id FROM plans WHERE plan_name = ?";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->execute([$plan_name]);
            
            if ($check_stmt->rowCount() > 0) {
                $error_message = 'يوجد خطة بنفس هذا الاسم مسبقاً';
            } else {
                // إنشاء الخطة الجديدة
                $insert_query = "INSERT INTO plans (plan_name, description, base_price, renewal_price, duration_months, features, status) VALUES (?, ?, ?, ?, ?, ?, ?)";
                $insert_stmt = $db->prepare($insert_query);
                
                if ($insert_stmt->execute([$plan_name, $description, $base_price, $renewal_price, $duration_months, $features, $status])) {
                    $success_message = 'تم إضافة الخطة بنجاح!';
                    
                    // مسح البيانات من النموذج
                    $_POST = array();
                } else {
                    $error_message = 'حدث خطأ أثناء إضافة الخطة';
                }
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة خطة جديدة - Workspace Management</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="dashboard.php" class="logo">Workspace Management - إدارة</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php">العملاء</a></li>
                    <li><a href="subscriptions.php">الاشتراكات</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="plans.php" style="color: #667eea;">الخطط</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">إضافة خطة جديدة</h1>
                <p class="dashboard-subtitle">إضافة خطة خدمة جديدة إلى النظام</p>
                <div style="margin-top: 1rem;">
                    <a href="plans.php" class="btn btn-secondary">العودة لقائمة الخطط</a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success fade-in"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger fade-in"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- نموذج إضافة الخطة -->
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 2rem; color: #333;">بيانات الخطة</h2>
                
                <form method="POST" action="" id="addPlanForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div class="form-group">
                            <label for="plan_name">اسم الخطة *</label>
                            <input type="text" id="plan_name" name="plan_name" class="form-control" 
                                   placeholder="أدخل اسم الخطة" required 
                                   value="<?php echo isset($_POST['plan_name']) ? htmlspecialchars($_POST['plan_name']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="base_price">السعر الأساسي (ر.س) *</label>
                            <input type="number" id="base_price" name="base_price" class="form-control" 
                                   placeholder="0.00" step="0.01" min="0" required 
                                   value="<?php echo isset($_POST['base_price']) ? htmlspecialchars($_POST['base_price']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="renewal_price">سعر التجديد (ر.س) *</label>
                            <input type="number" id="renewal_price" name="renewal_price" class="form-control" 
                                   placeholder="0.00" step="0.01" min="0" required 
                                   value="<?php echo isset($_POST['renewal_price']) ? htmlspecialchars($_POST['renewal_price']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="duration_months">مدة الخطة (بالأشهر) *</label>
                            <input type="number" id="duration_months" name="duration_months" class="form-control" 
                                   placeholder="12" min="1" required 
                                   value="<?php echo isset($_POST['duration_months']) ? htmlspecialchars($_POST['duration_months']) : '12'; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="status">حالة الخطة *</label>
                            <select id="status" name="status" class="form-control" required>
                                <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] === 'active') ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo (isset($_POST['status']) && $_POST['status'] === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group" style="margin-top: 2rem;">
                        <label for="description">وصف الخطة *</label>
                        <textarea id="description" name="description" class="form-control" 
                                  placeholder="أدخل وصف مفصل للخطة" rows="4" required><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="features">المميزات والخدمات</label>
                        <textarea id="features" name="features" class="form-control" 
                                  placeholder="اكتب المميزات مفصولة بفواصل أو في أسطر منفصلة" rows="6"><?php echo isset($_POST['features']) ? htmlspecialchars($_POST['features']) : ''; ?></textarea>
                        <small style="color: #666; font-size: 0.9rem;">مثال: تصميم موقع، استضافة، دومين، دعم فني</small>
                    </div>
                    
                    <div style="margin-top: 2rem; text-align: center;">
                        <button type="submit" name="add_plan" class="btn btn-primary" style="min-width: 200px;">
                            <span class="btn-text">إضافة الخطة</span>
                            <span class="loading" style="display: none;"></span>
                        </button>
                        <a href="plans.php" class="btn btn-secondary" style="margin-right: 1rem;">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        // تأثيرات النموذج
        document.getElementById('addPlanForm').addEventListener('submit', function(e) {
            const btnText = document.querySelector('.btn-text');
            const loading = document.querySelector('.loading');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            btnText.style.display = 'none';
            loading.style.display = 'inline-block';
            submitBtn.disabled = true;
        });
        
        // تحديث سعر التجديد تلقائياً عند تغيير السعر الأساسي
        document.getElementById('base_price').addEventListener('input', function() {
            const basePrice = parseFloat(this.value);
            const renewalPriceField = document.getElementById('renewal_price');
            
            if (basePrice && !renewalPriceField.value) {
                // اقتراح سعر تجديد (عادة أقل من السعر الأساسي)
                const suggestedRenewal = Math.round(basePrice * 0.6 * 100) / 100;
                renewalPriceField.value = suggestedRenewal;
            }
        });
        
        // التحقق من صحة الأسعار
        document.getElementById('renewal_price').addEventListener('input', function() {
            const basePrice = parseFloat(document.getElementById('base_price').value);
            const renewalPrice = parseFloat(this.value);
            
            if (basePrice && renewalPrice && renewalPrice > basePrice) {
                this.style.borderColor = '#ffc107';
                this.style.boxShadow = '0 0 0 3px rgba(255, 193, 7, 0.1)';
                
                // إظهار تحذير
                let warning = document.getElementById('price-warning');
                if (!warning) {
                    warning = document.createElement('small');
                    warning.id = 'price-warning';
                    warning.style.color = '#856404';
                    warning.textContent = 'تنبيه: سعر التجديد أعلى من السعر الأساسي';
                    this.parentElement.appendChild(warning);
                }
            } else {
                this.style.borderColor = '#e1e5e9';
                this.style.boxShadow = 'none';
                
                const warning = document.getElementById('price-warning');
                if (warning) {
                    warning.remove();
                }
            }
        });
        
        // معاينة المميزات
        document.getElementById('features').addEventListener('input', function() {
            const features = this.value;
            const lines = features.split('\n').filter(line => line.trim());
            
            // يمكن إضافة معاينة للمميزات هنا
        });
    </script>
</body>
</html>

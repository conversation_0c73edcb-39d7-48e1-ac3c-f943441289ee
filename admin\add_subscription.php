<?php
require_once '../config/database.php';
require_once '../includes/invoice_pdf.php';

// التحقق من صلاحيات الإدمن
if (!isLoggedIn() || !isAdmin()) {
    redirect('../index.php');
}

$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// جلب العملاء والخطط
$customers_query = "SELECT id, full_name, email FROM users WHERE user_type = 'customer' AND status = 'active' ORDER BY full_name";
$customers_stmt = $db->prepare($customers_query);
$customers_stmt->execute();
$customers = $customers_stmt->fetchAll();

$plans_query = "SELECT id, plan_name, base_price, renewal_price, duration_months FROM plans WHERE status = 'active' ORDER BY plan_name";
$plans_stmt = $db->prepare($plans_query);
$plans_stmt->execute();
$plans = $plans_stmt->fetchAll();

// معالجة إضافة اشتراك جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_subscription'])) {
    $customer_id = (int)$_POST['customer_id'];
    $plan_id = (int)$_POST['plan_id'];
    $subscription_name = sanitize($_POST['subscription_name']);
    $start_date = sanitize($_POST['start_date']);
    $duration_months = (int)$_POST['duration_months'];
    $subscription_price = floatval($_POST['subscription_price']);
    $renewal_price = floatval($_POST['renewal_price']);
    $service_type = sanitize($_POST['service_type']);
    $notes = sanitize($_POST['notes']);
    $create_invoice = isset($_POST['create_invoice']);
    
    // حساب تاريخ الانتهاء
    $end_date = date('Y-m-d', strtotime($start_date . ' + ' . $duration_months . ' months'));
    $renewal_date = date('Y-m-d', strtotime($end_date . ' - 30 days')); // تذكير قبل 30 يوم
    
    // التحقق من البيانات
    if (empty($customer_id) || empty($plan_id) || empty($subscription_name) || empty($start_date) || 
        $duration_months <= 0 || $subscription_price <= 0 || $renewal_price <= 0) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة بقيم صحيحة';
    } else {
        try {
            $db->beginTransaction();
            
            // إنشاء الاشتراك
            $insert_subscription_query = "
                INSERT INTO customer_subscriptions 
                (customer_id, plan_id, subscription_name, start_date, end_date, renewal_date, 
                 subscription_price, renewal_price, service_type, notes, status) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
            ";
            
            $insert_subscription_stmt = $db->prepare($insert_subscription_query);
            $subscription_success = $insert_subscription_stmt->execute([
                $customer_id, $plan_id, $subscription_name, $start_date, $end_date, $renewal_date,
                $subscription_price, $renewal_price, $service_type, $notes
            ]);
            
            if ($subscription_success) {
                $subscription_id = $db->lastInsertId();
                
                // إنشاء فاتورة إذا كان مطلوباً
                if ($create_invoice) {
                    $due_date = date('Y-m-d', strtotime($start_date . ' + 30 days'));
                    $invoice_notes = "فاتورة اشتراك: " . $subscription_name;
                    
                    $invoice_id = createInvoice($subscription_id, $customer_id, $subscription_price, $due_date, $invoice_notes);
                    
                    if (!$invoice_id) {
                        throw new Exception('فشل في إنشاء الفاتورة');
                    }
                }
                
                $db->commit();
                $success_message = 'تم إضافة الاشتراك بنجاح!' . ($create_invoice ? ' وتم إنشاء الفاتورة.' : '');
                
                // مسح البيانات من النموذج
                $_POST = array();
            } else {
                throw new Exception('فشل في إنشاء الاشتراك');
            }
        } catch (Exception $e) {
            $db->rollBack();
            $error_message = 'حدث خطأ: ' . $e->getMessage();
        }
    }
}

// إذا تم تمرير معرف العميل في الرابط
$selected_customer = isset($_GET['customer_id']) ? (int)$_GET['customer_id'] : 0;
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة اشتراك جديد - Workspace Management</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="dashboard.php" class="logo">Workspace Management - إدارة</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php">العملاء</a></li>
                    <li><a href="subscriptions.php" style="color: #667eea;">الاشتراكات</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="plans.php">الخطط</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">إضافة اشتراك جديد</h1>
                <p class="dashboard-subtitle">إضافة اشتراك جديد لأحد العملاء</p>
                <div style="margin-top: 1rem;">
                    <a href="subscriptions.php" class="btn btn-secondary">العودة لقائمة الاشتراكات</a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success fade-in"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger fade-in"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- نموذج إضافة الاشتراك -->
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 2rem; color: #333;">بيانات الاشتراك</h2>
                
                <form method="POST" action="" id="addSubscriptionForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div class="form-group">
                            <label for="customer_id">العميل *</label>
                            <select id="customer_id" name="customer_id" class="form-control" required>
                                <option value="">اختر العميل</option>
                                <?php foreach ($customers as $customer): ?>
                                    <option value="<?php echo $customer['id']; ?>" 
                                            <?php echo ($selected_customer == $customer['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($customer['full_name']) . ' (' . htmlspecialchars($customer['email']) . ')'; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="plan_id">الخطة *</label>
                            <select id="plan_id" name="plan_id" class="form-control" required>
                                <option value="">اختر الخطة</option>
                                <?php foreach ($plans as $plan): ?>
                                    <option value="<?php echo $plan['id']; ?>" 
                                            data-base-price="<?php echo $plan['base_price']; ?>"
                                            data-renewal-price="<?php echo $plan['renewal_price']; ?>"
                                            data-duration="<?php echo $plan['duration_months']; ?>">
                                        <?php echo htmlspecialchars($plan['plan_name']) . ' (' . number_format($plan['base_price'], 2) . ' ر.س)'; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="subscription_name">اسم الاشتراك *</label>
                            <input type="text" id="subscription_name" name="subscription_name" class="form-control" 
                                   placeholder="أدخل اسم مخصص للاشتراك" required 
                                   value="<?php echo isset($_POST['subscription_name']) ? htmlspecialchars($_POST['subscription_name']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="service_type">نوع الخدمة</label>
                            <input type="text" id="service_type" name="service_type" class="form-control" 
                                   placeholder="مثال: منيو، موقع، تصميم" 
                                   value="<?php echo isset($_POST['service_type']) ? htmlspecialchars($_POST['service_type']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="start_date">تاريخ البداية *</label>
                            <input type="date" id="start_date" name="start_date" class="form-control" required 
                                   value="<?php echo isset($_POST['start_date']) ? $_POST['start_date'] : date('Y-m-d'); ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="duration_months">مدة الاشتراك (بالأشهر) *</label>
                            <input type="number" id="duration_months" name="duration_months" class="form-control" 
                                   placeholder="12" min="1" required 
                                   value="<?php echo isset($_POST['duration_months']) ? $_POST['duration_months'] : '12'; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="subscription_price">سعر الاشتراك (ر.س) *</label>
                            <input type="number" id="subscription_price" name="subscription_price" class="form-control" 
                                   placeholder="0.00" step="0.01" min="0" required 
                                   value="<?php echo isset($_POST['subscription_price']) ? $_POST['subscription_price'] : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="renewal_price">سعر التجديد (ر.س) *</label>
                            <input type="number" id="renewal_price" name="renewal_price" class="form-control" 
                                   placeholder="0.00" step="0.01" min="0" required 
                                   value="<?php echo isset($_POST['renewal_price']) ? $_POST['renewal_price'] : ''; ?>">
                        </div>
                    </div>
                    
                    <div class="form-group" style="margin-top: 2rem;">
                        <label for="notes">ملاحظات</label>
                        <textarea id="notes" name="notes" class="form-control" 
                                  placeholder="أي ملاحظات إضافية حول الاشتراك" rows="4"><?php echo isset($_POST['notes']) ? htmlspecialchars($_POST['notes']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label style="display: flex; align-items: center; gap: 0.5rem;">
                            <input type="checkbox" name="create_invoice" value="1" 
                                   <?php echo isset($_POST['create_invoice']) ? 'checked' : ''; ?>>
                            إنشاء فاتورة تلقائياً للاشتراك
                        </label>
                        <small style="color: #666; font-size: 0.9rem;">سيتم إنشاء فاتورة بمبلغ الاشتراك مع تاريخ استحقاق 30 يوم</small>
                    </div>
                    
                    <div style="margin-top: 2rem; text-align: center;">
                        <button type="submit" name="add_subscription" class="btn btn-primary" style="min-width: 200px;">
                            <span class="btn-text">إضافة الاشتراك</span>
                            <span class="loading" style="display: none;"></span>
                        </button>
                        <a href="subscriptions.php" class="btn btn-secondary" style="margin-right: 1rem;">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        // تأثيرات النموذج
        document.getElementById('addSubscriptionForm').addEventListener('submit', function(e) {
            const btnText = document.querySelector('.btn-text');
            const loading = document.querySelector('.loading');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            btnText.style.display = 'none';
            loading.style.display = 'inline-block';
            submitBtn.disabled = true;
        });
        
        // تحديث البيانات عند اختيار خطة
        document.getElementById('plan_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            
            if (selectedOption.value) {
                const basePrice = selectedOption.getAttribute('data-base-price');
                const renewalPrice = selectedOption.getAttribute('data-renewal-price');
                const duration = selectedOption.getAttribute('data-duration');
                const planName = selectedOption.textContent.split(' (')[0];
                
                document.getElementById('subscription_price').value = basePrice;
                document.getElementById('renewal_price').value = renewalPrice;
                document.getElementById('duration_months').value = duration;
                
                // تحديث اسم الاشتراك إذا كان فارغاً
                const subscriptionNameField = document.getElementById('subscription_name');
                if (!subscriptionNameField.value) {
                    subscriptionNameField.value = planName;
                }
            }
        });
        
        // حساب تاريخ الانتهاء تلقائياً
        function updateEndDate() {
            const startDate = document.getElementById('start_date').value;
            const duration = parseInt(document.getElementById('duration_months').value);
            
            if (startDate && duration) {
                const start = new Date(startDate);
                const end = new Date(start.setMonth(start.getMonth() + duration));
                
                // عرض تاريخ الانتهاء للمستخدم
                let endDateInfo = document.getElementById('end-date-info');
                if (!endDateInfo) {
                    endDateInfo = document.createElement('small');
                    endDateInfo.id = 'end-date-info';
                    endDateInfo.style.color = '#667eea';
                    endDateInfo.style.display = 'block';
                    endDateInfo.style.marginTop = '0.5rem';
                    document.getElementById('duration_months').parentElement.appendChild(endDateInfo);
                }
                
                endDateInfo.textContent = 'تاريخ الانتهاء: ' + end.toLocaleDateString('ar-SA');
            }
        }
        
        document.getElementById('start_date').addEventListener('change', updateEndDate);
        document.getElementById('duration_months').addEventListener('input', updateEndDate);
        
        // تحديث اسم الاشتراك عند اختيار العميل والخطة
        function updateSubscriptionName() {
            const customerSelect = document.getElementById('customer_id');
            const planSelect = document.getElementById('plan_id');
            const subscriptionNameField = document.getElementById('subscription_name');
            
            if (customerSelect.value && planSelect.value && !subscriptionNameField.value) {
                const customerName = customerSelect.options[customerSelect.selectedIndex].textContent.split(' (')[0];
                const planName = planSelect.options[planSelect.selectedIndex].textContent.split(' (')[0];
                
                subscriptionNameField.value = planName + ' - ' + customerName;
            }
        }
        
        document.getElementById('customer_id').addEventListener('change', updateSubscriptionName);
        
        // تحديث تاريخ الانتهاء عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            updateEndDate();
        });
    </script>
</body>
</html>

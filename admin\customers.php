<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || $_SESSION['user_type'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة البحث
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$search_condition = '';
$search_params = [];

if (!empty($search)) {
    $search_condition = "WHERE (u.full_name LIKE ? OR u.email LIKE ? OR u.username LIKE ?)";
    $search_params = ["%$search%", "%$search%", "%$search%"];
}

// جلب العملاء مع إحصائياتهم
$customers_query = "
    SELECT u.*, 
           COUNT(DISTINCT cs.id) as total_subscriptions,
           COUNT(DISTINCT CASE WHEN cs.status = 'active' THEN cs.id END) as active_subscriptions,
           COALESCE(SUM(CASE WHEN i.status = 'paid' THEN i.amount END), 0) as total_paid
    FROM users u 
    LEFT JOIN customer_subscriptions cs ON u.id = cs.customer_id 
    LEFT JOIN invoices i ON u.id = i.customer_id 
    $search_condition
    AND u.user_type = 'customer'
    GROUP BY u.id 
    ORDER BY u.created_at DESC
";

$customers_stmt = $db->prepare($customers_query);
$customers_stmt->execute($search_params);
$customers = $customers_stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة العملاء - Workspace Management</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="dashboard.php" class="logo">Workspace Management - إدارة</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php" style="color: #667eea;">العملاء</a></li>
                    <li><a href="subscriptions.php">الاشتراكات</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="plans.php">الخطط</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">إدارة العملاء</h1>
                <p class="dashboard-subtitle">عرض وإدارة جميع عملاء النظام</p>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success fade-in"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger fade-in"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- أدوات البحث والإضافة -->
            <div class="table-container fade-in">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; flex-wrap: wrap; gap: 1rem;">
                    <div style="flex: 1; min-width: 300px;">
                        <form method="GET" action="" style="display: flex; gap: 1rem;">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="البحث بالاسم أو البريد الإلكتروني أو اسم المستخدم..." 
                                   value="<?php echo htmlspecialchars($search); ?>" style="flex: 1;">
                            <button type="submit" class="btn btn-primary">بحث</button>
                            <?php if (!empty($search)): ?>
                                <a href="customers.php" class="btn btn-secondary">مسح</a>
                            <?php endif; ?>
                        </form>
                    </div>
                    <div>
                        <a href="add_customer.php" class="btn btn-primary">إضافة عميل جديد</a>
                    </div>
                </div>

                <!-- جدول العملاء -->
                <?php if (!empty($customers)): ?>
                <div style="overflow-x: auto;">
                    <table class="table" id="customersTable">
                        <thead>
                            <tr>
                                <th>الاسم الكامل</th>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>رقم الهاتف</th>
                                <th>الاشتراكات</th>
                                <th>إجمالي المدفوعات</th>
                                <th>تاريخ التسجيل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($customers as $customer): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($customer['full_name']); ?></strong>
                                </td>
                                <td><?php echo htmlspecialchars($customer['username']); ?></td>
                                <td>
                                    <a href="mailto:<?php echo htmlspecialchars($customer['email']); ?>" 
                                       style="color: #667eea; text-decoration: none;">
                                        <?php echo htmlspecialchars($customer['email']); ?>
                                    </a>
                                </td>
                                <td>
                                    <?php if ($customer['phone']): ?>
                                        <a href="tel:<?php echo htmlspecialchars($customer['phone']); ?>" 
                                           style="color: #667eea; text-decoration: none;">
                                            <?php echo htmlspecialchars($customer['phone']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span style="color: #999;">غير محدد</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span style="color: #667eea; font-weight: 600;">
                                        <?php echo $customer['active_subscriptions']; ?>
                                    </span>
                                    /
                                    <?php echo $customer['total_subscriptions']; ?>
                                </td>
                                <td>
                                    <strong style="color: #28a745;">
                                        <?php echo number_format($customer['total_paid'], 2); ?> ر.س
                                    </strong>
                                </td>
                                <td><?php echo date('Y/m/d', strtotime($customer['created_at'])); ?></td>
                                <td>
                                    <?php if ($customer['status'] === 'active'): ?>
                                        <span class="badge badge-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge badge-danger">غير نشط</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                        <a href="customer_details.php?id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-sm btn-primary">التفاصيل</a>
                                        <a href="edit_customer.php?id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-sm btn-secondary">تعديل</a>
                                        <a href="add_subscription.php?customer_id=<?php echo $customer['id']; ?>" 
                                           class="btn btn-sm" style="background: #28a745; color: white;">إضافة اشتراك</a>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <?php if (!empty($search)): ?>
                        <h3>لا توجد نتائج للبحث "<?php echo htmlspecialchars($search); ?>"</h3>
                        <p>جرب البحث بكلمات مختلفة أو <a href="customers.php">عرض جميع العملاء</a></p>
                    <?php else: ?>
                        <h3>لا يوجد عملاء مسجلين حالياً</h3>
                        <p>ابدأ بإضافة عميل جديد</p>
                        <a href="add_customer.php" class="btn btn-primary">إضافة عميل جديد</a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        // إضافة تأثيرات للجدول
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات الصفوف
            const rows = document.querySelectorAll('#customersTable tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(102, 126, 234, 0.05)';
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'all 0.2s ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });
            
            // تأثيرات الشارات
            const badges = document.querySelectorAll('.badge');
            badges.forEach(badge => {
                badge.style.padding = '0.25rem 0.75rem';
                badge.style.borderRadius = '20px';
                badge.style.fontSize = '0.875rem';
                badge.style.fontWeight = '500';
                
                if (badge.classList.contains('badge-success')) {
                    badge.style.background = '#d4edda';
                    badge.style.color = '#155724';
                } else if (badge.classList.contains('badge-danger')) {
                    badge.style.background = '#f8d7da';
                    badge.style.color = '#721c24';
                }
            });
        });
    </script>
</body>
</html>

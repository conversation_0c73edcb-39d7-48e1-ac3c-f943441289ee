<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || $_SESSION['user_type'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'meedpsco_workspace';
$username = 'meedpsco_workspace';
$password_db = 'meedpsco_workspace';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password_db);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // إحصائيات عامة
    $general_stats = [];
    
    // إجمالي العملاء
    $customers_stmt = $pdo->query("SELECT COUNT(*) as total, COUNT(CASE WHEN status = 'active' THEN 1 END) as active FROM users WHERE user_type = 'customer'");
    $general_stats['customers'] = $customers_stmt->fetch();
    
    // إجمالي الاشتراكات
    $subscriptions_stmt = $pdo->query("SELECT COUNT(*) as total, COUNT(CASE WHEN status = 'active' THEN 1 END) as active, SUM(CASE WHEN status = 'active' THEN subscription_price ELSE 0 END) as revenue FROM customer_subscriptions");
    $general_stats['subscriptions'] = $subscriptions_stmt->fetch();
    
    // إجمالي الفواتير
    $invoices_stmt = $pdo->query("SELECT COUNT(*) as total, COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid, SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as revenue, SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending FROM invoices");
    $general_stats['invoices'] = $invoices_stmt->fetch();
    
    // إجمالي الخطط
    $plans_stmt = $pdo->query("SELECT COUNT(*) as total, COUNT(CASE WHEN status = 'active' THEN 1 END) as active FROM plans");
    $general_stats['plans'] = $plans_stmt->fetch();
    
    // تقرير الإيرادات الشهرية (آخر 6 أشهر)
    $monthly_revenue = [];
    for ($i = 5; $i >= 0; $i--) {
        $month = date('Y-m', strtotime("-$i months"));
        $stmt = $pdo->prepare("SELECT SUM(amount) as revenue FROM invoices WHERE status = 'paid' AND DATE_FORMAT(payment_date, '%Y-%m') = ?");
        $stmt->execute([$month]);
        $result = $stmt->fetch();
        $monthly_revenue[] = [
            'month' => date('Y/m', strtotime("-$i months")),
            'revenue' => $result['revenue'] ?: 0
        ];
    }
    
    // أفضل الخطط (الأكثر اشتراكاً)
    $top_plans_stmt = $pdo->query("
        SELECT p.plan_name, COUNT(cs.id) as subscriptions, SUM(cs.subscription_price) as revenue
        FROM plans p
        LEFT JOIN customer_subscriptions cs ON p.id = cs.plan_id
        GROUP BY p.id, p.plan_name
        ORDER BY subscriptions DESC
        LIMIT 5
    ");
    $top_plans = $top_plans_stmt->fetchAll();
    
    // العملاء الأكثر إنفاقاً
    $top_customers_stmt = $pdo->query("
        SELECT u.full_name, u.email, SUM(i.amount) as total_spent, COUNT(i.id) as invoices_count
        FROM users u
        JOIN invoices i ON u.id = i.customer_id
        WHERE i.status = 'paid'
        GROUP BY u.id, u.full_name, u.email
        ORDER BY total_spent DESC
        LIMIT 5
    ");
    $top_customers = $top_customers_stmt->fetchAll();
    
} catch (Exception $e) {
    $error_message = 'حدث خطأ في الاتصال بقاعدة البيانات';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - Meed Ps Workspace</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="dashboard.php" class="logo">Meed Ps - إدارة العملاء</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php">العملاء</a></li>
                    <li><a href="subscriptions.php">الاشتراكات</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="plans.php">الخطط</a></li>
                    <li><a href="reports.php" style="color: #0f3460;">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">التقارير والإحصائيات</h1>
                <p class="dashboard-subtitle">نظرة شاملة على أداء النظام والإيرادات</p>
            </div>

            <!-- الإحصائيات العامة -->
            <div class="stats-grid fade-in">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $general_stats['customers']['total']; ?></div>
                    <div class="stat-label">إجمالي العملاء</div>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">
                        نشط: <?php echo $general_stats['customers']['active']; ?>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $general_stats['subscriptions']['total']; ?></div>
                    <div class="stat-label">إجمالي الاشتراكات</div>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">
                        نشط: <?php echo $general_stats['subscriptions']['active']; ?>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($general_stats['invoices']['revenue'], 2); ?> ₪</div>
                    <div class="stat-label">إجمالي الإيرادات</div>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">
                        معلق: <?php echo number_format($general_stats['invoices']['pending'], 2); ?> ₪
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $general_stats['invoices']['total']; ?></div>
                    <div class="stat-label">إجمالي الفواتير</div>
                    <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">
                        مدفوع: <?php echo $general_stats['invoices']['paid']; ?>
                    </div>
                </div>
            </div>

            <!-- تقرير الإيرادات الشهرية -->
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 2rem; color: #333;">الإيرادات الشهرية (آخر 6 أشهر)</h2>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الشهر</th>
                                <th>الإيرادات</th>
                                <th>النسبة المئوية</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $max_revenue = max(array_column($monthly_revenue, 'revenue'));
                            foreach ($monthly_revenue as $month_data): 
                                $percentage = $max_revenue > 0 ? ($month_data['revenue'] / $max_revenue) * 100 : 0;
                            ?>
                            <tr>
                                <td><strong><?php echo $month_data['month']; ?></strong></td>
                                <td><strong style="color: #0f3460;"><?php echo number_format($month_data['revenue'], 2); ?> ₪</strong></td>
                                <td>
                                    <div style="display: flex; align-items: center; gap: 10px;">
                                        <div style="background: #e9ecef; height: 20px; width: 100px; border-radius: 10px; overflow: hidden;">
                                            <div style="background: #0f3460; height: 100%; width: <?php echo $percentage; ?>%; transition: width 0.3s ease;"></div>
                                        </div>
                                        <span><?php echo number_format($percentage, 1); ?>%</span>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- أفضل الخطط -->
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 2rem; color: #333;">أفضل الخطط (الأكثر اشتراكاً)</h2>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم الخطة</th>
                                <th>عدد الاشتراكات</th>
                                <th>إجمالي الإيرادات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_plans as $plan): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($plan['plan_name']); ?></strong></td>
                                <td><?php echo $plan['subscriptions']; ?></td>
                                <td><strong style="color: #0f3460;"><?php echo number_format($plan['revenue'], 2); ?> ₪</strong></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- أفضل العملاء -->
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 2rem; color: #333;">العملاء الأكثر إنفاقاً</h2>
                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>اسم العميل</th>
                                <th>البريد الإلكتروني</th>
                                <th>إجمالي الإنفاق</th>
                                <th>عدد الفواتير</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($top_customers as $customer): ?>
                            <tr>
                                <td><strong><?php echo htmlspecialchars($customer['full_name']); ?></strong></td>
                                <td><?php echo htmlspecialchars($customer['email']); ?></td>
                                <td><strong style="color: #0f3460;"><?php echo number_format($customer['total_spent'], 2); ?> ₪</strong></td>
                                <td><?php echo $customer['invoices_count']; ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- أزرار التصدير -->
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 2rem; color: #333;">تصدير التقارير</h2>
                <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                    <button onclick="window.print()" class="btn btn-primary">طباعة التقرير</button>
                    <button onclick="exportToCSV()" class="btn btn-secondary">تصدير CSV</button>
                    <a href="detailed_report.php" class="btn" style="background: #28a745; color: white;">تقرير مفصل</a>
                </div>
            </div>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        function exportToCSV() {
            alert('ميزة تصدير CSV ستكون متاحة قريباً');
        }
        
        // تأثيرات الجداول
        document.addEventListener('DOMContentLoaded', function() {
            const tables = document.querySelectorAll('.table tbody tr');
            tables.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(15, 52, 96, 0.05)';
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'all 0.2s ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>

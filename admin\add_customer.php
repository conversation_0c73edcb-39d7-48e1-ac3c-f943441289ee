<?php
require_once '../config/database.php';

// التحقق من صلاحيات الإدمن
if (!isLoggedIn() || !isAdmin()) {
    redirect('../index.php');
}

$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة إضافة عميل جديد
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['add_customer'])) {
    $username = sanitize($_POST['username']);
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    $full_name = sanitize($_POST['full_name']);
    $phone = sanitize($_POST['phone']);
    $status = sanitize($_POST['status']);
    
    // التحقق من البيانات
    if (empty($username) || empty($email) || empty($password) || empty($full_name)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (strlen($password) < 6) {
        $error_message = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'البريد الإلكتروني غير صحيح';
    } else {
        try {
            // التحقق من عدم وجود المستخدم مسبقاً
            $check_query = "SELECT id FROM users WHERE email = ? OR username = ?";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->execute([$email, $username]);
            
            if ($check_stmt->rowCount() > 0) {
                $error_message = 'البريد الإلكتروني أو اسم المستخدم مستخدم مسبقاً';
            } else {
                // إنشاء العميل الجديد
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                $insert_query = "INSERT INTO users (username, email, password, full_name, phone, user_type, status) VALUES (?, ?, ?, ?, ?, 'customer', ?)";
                $insert_stmt = $db->prepare($insert_query);
                
                if ($insert_stmt->execute([$username, $email, $hashed_password, $full_name, $phone, $status])) {
                    $success_message = 'تم إضافة العميل بنجاح!';
                    
                    // مسح البيانات من النموذج
                    $_POST = array();
                } else {
                    $error_message = 'حدث خطأ أثناء إضافة العميل';
                }
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ في النظام، يرجى المحاولة لاحقاً';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة عميل جديد - Workspace Management</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="dashboard.php" class="logo">Workspace Management - إدارة</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php" style="color: #667eea;">العملاء</a></li>
                    <li><a href="subscriptions.php">الاشتراكات</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="plans.php">الخطط</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">إضافة عميل جديد</h1>
                <p class="dashboard-subtitle">إضافة عميل جديد إلى النظام</p>
                <div style="margin-top: 1rem;">
                    <a href="customers.php" class="btn btn-secondary">العودة لقائمة العملاء</a>
                </div>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success fade-in"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger fade-in"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- نموذج إضافة العميل -->
            <div class="table-container fade-in">
                <h2 style="margin-bottom: 2rem; color: #333;">بيانات العميل</h2>
                
                <form method="POST" action="" id="addCustomerForm">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                        <div class="form-group">
                            <label for="full_name">الاسم الكامل *</label>
                            <input type="text" id="full_name" name="full_name" class="form-control" 
                                   placeholder="أدخل الاسم الكامل" required 
                                   value="<?php echo isset($_POST['full_name']) ? htmlspecialchars($_POST['full_name']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="username">اسم المستخدم *</label>
                            <input type="text" id="username" name="username" class="form-control" 
                                   placeholder="اختر اسم مستخدم فريد" required 
                                   value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">البريد الإلكتروني *</label>
                            <input type="email" id="email" name="email" class="form-control" 
                                   placeholder="أدخل البريد الإلكتروني" required 
                                   value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">رقم الهاتف</label>
                            <input type="tel" id="phone" name="phone" class="form-control" 
                                   placeholder="أدخل رقم الهاتف" 
                                   value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                        </div>
                        
                        <div class="form-group">
                            <label for="password">كلمة المرور *</label>
                            <input type="password" id="password" name="password" class="form-control" 
                                   placeholder="أدخل كلمة مرور قوية (6 أحرف على الأقل)" required>
                            <small style="color: #666; font-size: 0.9rem;">يجب أن تحتوي على 6 أحرف على الأقل</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="status">حالة الحساب *</label>
                            <select id="status" name="status" class="form-control" required>
                                <option value="active" <?php echo (isset($_POST['status']) && $_POST['status'] === 'active') ? 'selected' : ''; ?>>نشط</option>
                                <option value="inactive" <?php echo (isset($_POST['status']) && $_POST['status'] === 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                            </select>
                        </div>
                    </div>
                    
                    <div style="margin-top: 2rem; text-align: center;">
                        <button type="submit" name="add_customer" class="btn btn-primary" style="min-width: 200px;">
                            <span class="btn-text">إضافة العميل</span>
                            <span class="loading" style="display: none;"></span>
                        </button>
                        <a href="customers.php" class="btn btn-secondary" style="margin-right: 1rem;">إلغاء</a>
                    </div>
                </form>
            </div>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        // تأثيرات النموذج
        document.getElementById('addCustomerForm').addEventListener('submit', function(e) {
            const btnText = document.querySelector('.btn-text');
            const loading = document.querySelector('.loading');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            btnText.style.display = 'none';
            loading.style.display = 'inline-block';
            submitBtn.disabled = true;
        });
        
        // توليد اسم مستخدم تلقائي من الاسم الكامل
        document.getElementById('full_name').addEventListener('input', function() {
            const fullName = this.value;
            const usernameField = document.getElementById('username');
            
            if (fullName && !usernameField.value) {
                // تحويل الاسم إلى اسم مستخدم
                let username = fullName.toLowerCase()
                    .replace(/\s+/g, '_')
                    .replace(/[^a-z0-9_]/g, '');
                
                usernameField.value = username;
            }
        });
        
        // التحقق من قوة كلمة المرور
        document.getElementById('password').addEventListener('input', function() {
            const password = this.value;
            const strength = getPasswordStrength(password);
            
            // تغيير لون الحقل حسب قوة كلمة المرور
            if (password.length === 0) {
                this.style.borderColor = '#e1e5e9';
            } else if (strength < 2) {
                this.style.borderColor = '#dc3545';
            } else if (strength < 4) {
                this.style.borderColor = '#ffc107';
            } else {
                this.style.borderColor = '#28a745';
            }
        });
        
        function getPasswordStrength(password) {
            let strength = 0;
            if (password.length >= 6) strength++;
            if (password.match(/[a-z]/)) strength++;
            if (password.match(/[A-Z]/)) strength++;
            if (password.match(/[0-9]/)) strength++;
            if (password.match(/[^a-zA-Z0-9]/)) strength++;
            return strength;
        }
        
        // التحقق من صحة البريد الإلكتروني
        document.getElementById('email').addEventListener('blur', function() {
            const email = this.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            
            if (email && !emailRegex.test(email)) {
                this.style.borderColor = '#dc3545';
                this.style.boxShadow = '0 0 0 3px rgba(220, 53, 69, 0.1)';
            } else {
                this.style.borderColor = '#e1e5e9';
                this.style.boxShadow = 'none';
            }
        });
    </script>
</body>
</html>

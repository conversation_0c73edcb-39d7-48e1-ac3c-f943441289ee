<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || $_SESSION['user_type'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$database = new Database();
$db = $database->getConnection();

$success_message = '';
$error_message = '';

// معالجة تحديث حالة الفاتورة
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_status'])) {
    $invoice_id = (int)$_POST['invoice_id'];
    $new_status = sanitize($_POST['status']);
    $payment_date = ($new_status === 'paid') ? date('Y-m-d') : null;
    
    try {
        $update_query = "UPDATE invoices SET status = ?, payment_date = ? WHERE id = ?";
        $update_stmt = $db->prepare($update_query);
        
        if ($update_stmt->execute([$new_status, $payment_date, $invoice_id])) {
            $success_message = 'تم تحديث حالة الفاتورة بنجاح';
        } else {
            $error_message = 'حدث خطأ أثناء تحديث الفاتورة';
        }
    } catch (Exception $e) {
        $error_message = 'حدث خطأ في النظام';
    }
}

// معالجة البحث والفلترة
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';

$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(i.invoice_number LIKE ? OR u.full_name LIKE ? OR u.email LIKE ?)";
    $params[] = "%$search%";
    $params[] = "%$search%";
    $params[] = "%$search%";
}

if (!empty($status_filter)) {
    $where_conditions[] = "i.status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// جلب الفواتير مع بيانات العملاء والاشتراكات
$invoices_query = "
    SELECT i.*, 
           u.full_name, u.email,
           cs.subscription_name
    FROM invoices i
    JOIN users u ON i.customer_id = u.id
    LEFT JOIN customer_subscriptions cs ON i.subscription_id = cs.id
    $where_clause
    ORDER BY i.created_at DESC
";

$invoices_stmt = $db->prepare($invoices_query);
$invoices_stmt->execute($params);
$invoices = $invoices_stmt->fetchAll();

// إحصائيات سريعة
$stats_query = "
    SELECT 
        COUNT(*) as total_invoices,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_invoices,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_invoices,
        COUNT(CASE WHEN status = 'overdue' THEN 1 END) as overdue_invoices,
        SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as total_revenue,
        SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END) as pending_amount
    FROM invoices
";
$stats_stmt = $db->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير - Workspace Management</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="dashboard.php" class="logo">Workspace Management - إدارة</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php">العملاء</a></li>
                    <li><a href="subscriptions.php">الاشتراكات</a></li>
                    <li><a href="invoices.php" style="color: #667eea;">الفواتير</a></li>
                    <li><a href="plans.php">الخطط</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">إدارة الفواتير</h1>
                <p class="dashboard-subtitle">عرض وإدارة جميع فواتير النظام</p>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success fade-in"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger fade-in"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- الإحصائيات السريعة -->
            <div class="stats-grid fade-in">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_invoices']; ?></div>
                    <div class="stat-label">إجمالي الفواتير</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['paid_invoices']; ?></div>
                    <div class="stat-label">الفواتير المدفوعة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['total_revenue'], 2); ?> ₪</div>
                    <div class="stat-label">إجمالي الإيرادات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['pending_amount'], 2); ?> ₪</div>
                    <div class="stat-label">المبالغ المعلقة</div>
                </div>
            </div>

            <!-- أدوات البحث والفلترة -->
            <div class="table-container fade-in">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; flex-wrap: wrap; gap: 1rem;">
                    <div style="flex: 1; min-width: 300px;">
                        <form method="GET" action="" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="البحث برقم الفاتورة أو اسم العميل..." 
                                   value="<?php echo htmlspecialchars($search); ?>" style="flex: 1; min-width: 200px;">
                            
                            <select name="status" class="form-control" style="min-width: 150px;">
                                <option value="">جميع الحالات</option>
                                <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلق</option>
                                <option value="paid" <?php echo $status_filter === 'paid' ? 'selected' : ''; ?>>مدفوع</option>
                                <option value="overdue" <?php echo $status_filter === 'overdue' ? 'selected' : ''; ?>>متأخر</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                            
                            <button type="submit" class="btn btn-primary">بحث</button>
                            <?php if (!empty($search) || !empty($status_filter)): ?>
                                <a href="invoices.php" class="btn btn-secondary">مسح</a>
                            <?php endif; ?>
                        </form>
                    </div>
                    <div>
                        <a href="create_invoice.php" class="btn btn-primary">إنشاء فاتورة جديدة</a>
                    </div>
                </div>

                <!-- جدول الفواتير -->
                <?php if (!empty($invoices)): ?>
                <div style="overflow-x: auto;">
                    <table class="table" id="invoicesTable">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>الخدمة</th>
                                <th>المبلغ</th>
                                <th>تاريخ الإصدار</th>
                                <th>تاريخ الاستحقاق</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($invoices as $invoice): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($invoice['full_name']); ?></strong><br>
                                    <small style="color: #666;"><?php echo htmlspecialchars($invoice['email']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($invoice['subscription_name'] ?: 'غير محدد'); ?></td>
                                <td>
                                    <strong style="color: #667eea;">
                                        <?php echo number_format($invoice['amount'], 2); ?> ₪
                                    </strong>
                                </td>
                                <td><?php echo date('Y/m/d', strtotime($invoice['invoice_date'])); ?></td>
                                <td>
                                    <?php 
                                    $due_date = new DateTime($invoice['due_date']);
                                    $now = new DateTime();
                                    $is_overdue = $now > $due_date && $invoice['status'] !== 'paid';
                                    ?>
                                    <span style="color: <?php echo $is_overdue ? '#dc3545' : '#333'; ?>">
                                        <?php echo date('Y/m/d', strtotime($invoice['due_date'])); ?>
                                    </span>
                                    <?php if ($is_overdue): ?>
                                        <br><small style="color: #dc3545;">متأخر</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    $status_text = '';
                                    switch ($invoice['status']) {
                                        case 'paid':
                                            $status_class = 'success';
                                            $status_text = 'مدفوع';
                                            break;
                                        case 'pending':
                                            $status_class = 'warning';
                                            $status_text = 'معلق';
                                            break;
                                        case 'overdue':
                                            $status_class = 'danger';
                                            $status_text = 'متأخر';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'secondary';
                                            $status_text = 'ملغي';
                                            break;
                                    }
                                    ?>
                                    <span class="badge badge-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                </td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                        <a href="invoice_pdf.php?id=<?php echo $invoice['id']; ?>" 
                                           class="btn btn-sm btn-primary" target="_blank">عرض</a>
                                        <a href="invoice_pdf.php?id=<?php echo $invoice['id']; ?>&download=1" 
                                           class="btn btn-sm btn-secondary">تحميل</a>
                                        <?php if ($invoice['status'] !== 'paid'): ?>
                                            <button onclick="updateInvoiceStatus(<?php echo $invoice['id']; ?>, 'paid')" 
                                                    class="btn btn-sm" style="background: #28a745; color: white;">تأكيد الدفع</button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <?php if (!empty($search) || !empty($status_filter)): ?>
                        <h3>لا توجد نتائج للبحث</h3>
                        <p>جرب البحث بكلمات مختلفة أو <a href="invoices.php">عرض جميع الفواتير</a></p>
                    <?php else: ?>
                        <h3>لا توجد فواتير حالياً</h3>
                        <p>ابدأ بإنشاء فاتورة جديدة</p>
                        <a href="create_invoice.php" class="btn btn-primary">إنشاء فاتورة جديدة</a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <!-- نموذج تحديث حالة الفاتورة -->
    <form id="updateStatusForm" method="POST" style="display: none;">
        <input type="hidden" name="invoice_id" id="invoiceId">
        <input type="hidden" name="status" id="invoiceStatus">
        <input type="hidden" name="update_status" value="1">
    </form>

    <script src="../assets/js/main.js"></script>
    <script>
        function updateInvoiceStatus(invoiceId, status) {
            if (confirm('هل أنت متأكد من تحديث حالة الفاتورة؟')) {
                document.getElementById('invoiceId').value = invoiceId;
                document.getElementById('invoiceStatus').value = status;
                document.getElementById('updateStatusForm').submit();
            }
        }
        
        // إضافة تأثيرات للجدول
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات الصفوف
            const rows = document.querySelectorAll('#invoicesTable tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(102, 126, 234, 0.05)';
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'all 0.2s ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });
            
            // تأثيرات الشارات
            const badges = document.querySelectorAll('.badge');
            badges.forEach(badge => {
                badge.style.padding = '0.25rem 0.75rem';
                badge.style.borderRadius = '20px';
                badge.style.fontSize = '0.875rem';
                badge.style.fontWeight = '500';
                
                if (badge.classList.contains('badge-success')) {
                    badge.style.background = '#d4edda';
                    badge.style.color = '#155724';
                } else if (badge.classList.contains('badge-danger')) {
                    badge.style.background = '#f8d7da';
                    badge.style.color = '#721c24';
                } else if (badge.classList.contains('badge-warning')) {
                    badge.style.background = '#fff3cd';
                    badge.style.color = '#856404';
                } else if (badge.classList.contains('badge-secondary')) {
                    badge.style.background = '#e2e3e5';
                    badge.style.color = '#383d41';
                }
            });
        });
    </script>
</body>
</html>

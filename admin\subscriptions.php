<?php
session_start();

// التحقق من تسجيل الدخول والصلاحيات
if (!isset($_SESSION['logged_in']) || $_SESSION['logged_in'] !== true || $_SESSION['user_type'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'meedpsco_workspace';
$username = 'meedpsco_workspace';
$password_db = 'meedpsco_workspace';

$success_message = '';
$error_message = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password_db);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // معالجة البحث والفلترة
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $status_filter = isset($_GET['status']) ? trim($_GET['status']) : '';
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(cs.subscription_name LIKE ? OR u.full_name LIKE ? OR u.email LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "cs.status = ?";
        $params[] = $status_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // جلب الاشتراكات مع بيانات العملاء والخطط
    $subscriptions_query = "
        SELECT cs.*, 
               u.full_name, u.email,
               p.plan_name
        FROM customer_subscriptions cs
        JOIN users u ON cs.customer_id = u.id
        LEFT JOIN plans p ON cs.plan_id = p.id
        $where_clause
        ORDER BY cs.created_at DESC
    ";
    
    $subscriptions_stmt = $pdo->prepare($subscriptions_query);
    $subscriptions_stmt->execute($params);
    $subscriptions = $subscriptions_stmt->fetchAll();
    
    // إحصائيات سريعة
    $stats_query = "
        SELECT 
            COUNT(*) as total_subscriptions,
            COUNT(CASE WHEN status = 'active' THEN 1 END) as active_subscriptions,
            COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired_subscriptions,
            COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_subscriptions,
            SUM(CASE WHEN status = 'active' THEN subscription_price ELSE 0 END) as active_revenue
        FROM customer_subscriptions
    ";
    $stats_stmt = $pdo->prepare($stats_query);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch();
    
} catch (Exception $e) {
    $error_message = 'حدث خطأ في الاتصال بقاعدة البيانات';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الاشتراكات - Meed Ps Workspace</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="../assets/images/favicon.ico">
</head>
<body>
    <!-- الهيدر -->
    <header class="header">
        <div class="container">
            <a href="dashboard.php" class="logo">Meed Ps - إدارة العملاء</a>
            <nav>
                <ul class="nav-menu">
                    <li><a href="dashboard.php">الرئيسية</a></li>
                    <li><a href="customers.php">العملاء</a></li>
                    <li><a href="subscriptions.php" style="color: #0f3460;">الاشتراكات</a></li>
                    <li><a href="invoices.php">الفواتير</a></li>
                    <li><a href="plans.php">الخطط</a></li>
                    <li><a href="reports.php">التقارير</a></li>
                    <li><a href="../customer/logout.php" style="color: #dc3545;">تسجيل الخروج</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="dashboard">
        <div class="container">
            <!-- عنوان الصفحة -->
            <div class="dashboard-header fade-in">
                <h1 class="dashboard-title">إدارة الاشتراكات</h1>
                <p class="dashboard-subtitle">عرض وإدارة جميع اشتراكات العملاء</p>
            </div>

            <?php if ($success_message): ?>
                <div class="alert alert-success fade-in"><?php echo $success_message; ?></div>
            <?php endif; ?>

            <?php if ($error_message): ?>
                <div class="alert alert-danger fade-in"><?php echo $error_message; ?></div>
            <?php endif; ?>

            <!-- الإحصائيات السريعة -->
            <div class="stats-grid fade-in">
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['total_subscriptions']; ?></div>
                    <div class="stat-label">إجمالي الاشتراكات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['active_subscriptions']; ?></div>
                    <div class="stat-label">الاشتراكات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo number_format($stats['active_revenue'], 2); ?> ₪</div>
                    <div class="stat-label">إيرادات الاشتراكات النشطة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?php echo $stats['expired_subscriptions']; ?></div>
                    <div class="stat-label">الاشتراكات المنتهية</div>
                </div>
            </div>

            <!-- أدوات البحث والفلترة -->
            <div class="table-container fade-in">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; flex-wrap: wrap; gap: 1rem;">
                    <div style="flex: 1; min-width: 300px;">
                        <form method="GET" action="" style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            <input type="text" name="search" class="form-control" 
                                   placeholder="البحث باسم الاشتراك أو العميل..." 
                                   value="<?php echo htmlspecialchars($search); ?>" style="flex: 1; min-width: 200px;">
                            
                            <select name="status" class="form-control" style="min-width: 150px;">
                                <option value="">جميع الحالات</option>
                                <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                                <option value="expired" <?php echo $status_filter === 'expired' ? 'selected' : ''; ?>>منتهي</option>
                                <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                            </select>
                            
                            <button type="submit" class="btn btn-primary">بحث</button>
                            <?php if (!empty($search) || !empty($status_filter)): ?>
                                <a href="subscriptions.php" class="btn btn-secondary">مسح</a>
                            <?php endif; ?>
                        </form>
                    </div>
                    <div>
                        <a href="add_subscription.php" class="btn btn-primary">إضافة اشتراك جديد</a>
                    </div>
                </div>

                <!-- جدول الاشتراكات -->
                <?php if (!empty($subscriptions)): ?>
                <div style="overflow-x: auto;">
                    <table class="table" id="subscriptionsTable">
                        <thead>
                            <tr>
                                <th>اسم الاشتراك</th>
                                <th>العميل</th>
                                <th>الخطة</th>
                                <th>تاريخ البداية</th>
                                <th>تاريخ الانتهاء</th>
                                <th>السعر</th>
                                <th>نوع الخدمة</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subscriptions as $subscription): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($subscription['subscription_name']); ?></strong>
                                </td>
                                <td>
                                    <strong><?php echo htmlspecialchars($subscription['full_name']); ?></strong><br>
                                    <small style="color: #666;"><?php echo htmlspecialchars($subscription['email']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($subscription['plan_name'] ?: 'غير محدد'); ?></td>
                                <td><?php echo date('Y/m/d', strtotime($subscription['start_date'])); ?></td>
                                <td>
                                    <?php 
                                    $end_date = new DateTime($subscription['end_date']);
                                    $now = new DateTime();
                                    $is_expired = $now > $end_date && $subscription['status'] === 'active';
                                    ?>
                                    <span style="color: <?php echo $is_expired ? '#dc3545' : '#333'; ?>">
                                        <?php echo date('Y/m/d', strtotime($subscription['end_date'])); ?>
                                    </span>
                                    <?php if ($is_expired): ?>
                                        <br><small style="color: #dc3545;">منتهي</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <strong style="color: #0f3460;">
                                        <?php echo number_format($subscription['subscription_price'], 2); ?> ₪
                                    </strong>
                                </td>
                                <td><?php echo htmlspecialchars($subscription['service_type'] ?: 'غير محدد'); ?></td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    $status_text = '';
                                    switch ($subscription['status']) {
                                        case 'active':
                                            $status_class = 'success';
                                            $status_text = 'نشط';
                                            break;
                                        case 'expired':
                                            $status_class = 'warning';
                                            $status_text = 'منتهي';
                                            break;
                                        case 'cancelled':
                                            $status_class = 'danger';
                                            $status_text = 'ملغي';
                                            break;
                                    }
                                    ?>
                                    <span class="badge badge-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                </td>
                                <td>
                                    <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                                        <a href="subscription_details.php?id=<?php echo $subscription['id']; ?>" 
                                           class="btn btn-sm btn-primary">التفاصيل</a>
                                        <a href="edit_subscription.php?id=<?php echo $subscription['id']; ?>" 
                                           class="btn btn-sm btn-secondary">تعديل</a>
                                        <?php if ($subscription['status'] === 'active'): ?>
                                            <a href="renew_subscription.php?id=<?php echo $subscription['id']; ?>" 
                                               class="btn btn-sm" style="background: #28a745; color: white;">تجديد</a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div style="text-align: center; padding: 3rem; color: #666;">
                    <?php if (!empty($search) || !empty($status_filter)): ?>
                        <h3>لا توجد نتائج للبحث</h3>
                        <p>جرب البحث بكلمات مختلفة أو <a href="subscriptions.php">عرض جميع الاشتراكات</a></p>
                    <?php else: ?>
                        <h3>لا توجد اشتراكات حالياً</h3>
                        <p>ابدأ بإضافة اشتراك جديد</p>
                        <a href="add_subscription.php" class="btn btn-primary">إضافة اشتراك جديد</a>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </main>

    <script src="../assets/js/main.js"></script>
    <script>
        // إضافة تأثيرات للجدول
        document.addEventListener('DOMContentLoaded', function() {
            // تأثيرات الصفوف
            const rows = document.querySelectorAll('#subscriptionsTable tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(15, 52, 96, 0.05)';
                    this.style.transform = 'scale(1.01)';
                    this.style.transition = 'all 0.2s ease';
                });
                
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                    this.style.transform = 'scale(1)';
                });
            });
            
            // تأثيرات الشارات
            const badges = document.querySelectorAll('.badge');
            badges.forEach(badge => {
                badge.style.padding = '0.25rem 0.75rem';
                badge.style.borderRadius = '20px';
                badge.style.fontSize = '0.875rem';
                badge.style.fontWeight = '500';
                
                if (badge.classList.contains('badge-success')) {
                    badge.style.background = '#d4edda';
                    badge.style.color = '#155724';
                } else if (badge.classList.contains('badge-danger')) {
                    badge.style.background = '#f8d7da';
                    badge.style.color = '#721c24';
                } else if (badge.classList.contains('badge-warning')) {
                    badge.style.background = '#fff3cd';
                    badge.style.color = '#856404';
                }
            });
        });
    </script>
</body>
</html>

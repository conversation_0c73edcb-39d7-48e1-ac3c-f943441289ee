<?php
require_once '../config/database.php';
require_once '../includes/invoice_pdf.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect('../index.php');
}

// التحقق من وجود معرف الفاتورة
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    redirect('invoices.php');
}

$invoice_id = (int)$_GET['id'];

try {
    // إنشاء كائن الفاتورة
    $invoicePDF = new InvoicePDF($invoice_id);
    $invoice_data = $invoicePDF->getInvoiceData();
    
    // التحقق من الصلاحيات
    if (!isAdmin() && $invoice_data['customer_id'] != $_SESSION['user_id']) {
        redirect('../index.php');
    }
    
    // إذا كان المطلوب تحميل الفاتورة
    if (isset($_GET['download'])) {
        echo $invoicePDF->downloadPDF();
        exit;
    }
    
    // عرض الفاتورة في المتصفح
    echo $invoicePDF->generatePDF();
    
} catch (Exception $e) {
    // في حالة حدوث خطأ
    ?>
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>خطأ - Workspace Management</title>
        <link rel="stylesheet" href="../assets/css/style.css">
    </head>
    <body>
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <h1 style="color: #dc3545;">خطأ</h1>
                    <p><?php echo htmlspecialchars($e->getMessage()); ?></p>
                </div>
                <div style="text-align: center;">
                    <a href="javascript:history.back()" class="btn btn-secondary">العودة</a>
                </div>
            </div>
        </div>
    </body>
    </html>
    <?php
}
?>

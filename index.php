<?php
session_start();

// إعدادات قاعدة البيانات
$host = 'localhost';
$dbname = 'meedpsco_workspace';
$username = 'meedpsco_workspace';
$password_db = 'meedpsco_workspace';

$error_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['login'])) {
    $email = trim($_POST['email']);
    $password = $_POST['password'];

    if (empty($email) || empty($password)) {
        $error_message = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        try {
            $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8", $username, $password_db);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

            $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? AND status = 'active'");
            $stmt->execute([$email]);

            if ($stmt->rowCount() == 1) {
                $user = $stmt->fetch();

                if (password_verify($password, $user['password'])) {
                    // تسجيل الدخول بنجاح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['email'] = $user['email'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['user_type'] = $user['user_type'];
                    $_SESSION['logged_in'] = true;

                    // إعادة التوجيه
                    if ($user['user_type'] === 'admin') {
                        header("Location: admin/dashboard.php");
                        exit();
                    } else {
                        header("Location: customer/dashboard.php");
                        exit();
                    }
                } else {
                    $error_message = 'كلمة المرور غير صحيحة';
                }
            } else {
                $error_message = 'البريد الإلكتروني غير مسجل أو الحساب غير مفعل';
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ في الاتصال بقاعدة البيانات';
        }
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - Meed Ps Workspace</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <div class="login-container">
        <div class="login-card fade-in">
            <div class="login-header">
                <h1>مرحباً بك في Meed Ps</h1>
                <p>سجل دخولك للوصول إلى لوحة إدارة العملاء والخدمات</p>
            </div>
            
            <?php if ($error_message): ?>
                <div class="alert alert-danger"><?php echo $error_message; ?></div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success"><?php echo $success_message; ?></div>
            <?php endif; ?>
            
            <form method="POST" action="" id="loginForm">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <input type="email" id="email" name="email" class="form-control" 
                           placeholder="أدخل بريدك الإلكتروني" required 
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <input type="password" id="password" name="password" class="form-control" 
                           placeholder="أدخل كلمة المرور" required>
                </div>
                
                <button type="submit" name="login" class="btn btn-primary">
                    <span class="btn-text">تسجيل الدخول</span>
                    <span class="loading" style="display: none;"></span>
                </button>
            </form>
            
            <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e1e5e9;">
                <p style="color: #666; margin-bottom: 1rem;">ليس لديك حساب؟</p>
                <a href="register.php" class="btn btn-secondary">إنشاء حساب جديد</a>
            </div>
            
            <div style="text-align: center; margin-top: 1rem;">
                <small style="color: #999;">
                    للدعم الفني: <a href="mailto:<?php echo ADMIN_EMAIL; ?>" style="color: #0f3460;"><?php echo ADMIN_EMAIL; ?></a>
                </small>
            </div>
        </div>
    </div>

    <script src="assets/js/main.js"></script>
    <script>
        // تأثيرات تفاعلية لنموذج تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const btnText = document.querySelector('.btn-text');
            const loading = document.querySelector('.loading');
            const submitBtn = document.querySelector('button[type="submit"]');
            
            btnText.style.display = 'none';
            loading.style.display = 'inline-block';
            submitBtn.disabled = true;
            
            // إذا كان هناك خطأ في التحقق، إعادة تفعيل الزر
            setTimeout(() => {
                if (document.querySelector('.alert-danger')) {
                    btnText.style.display = 'inline';
                    loading.style.display = 'none';
                    submitBtn.disabled = false;
                }
            }, 100);
        });
        
        // تأثير التركيز على الحقول
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.2s ease';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
